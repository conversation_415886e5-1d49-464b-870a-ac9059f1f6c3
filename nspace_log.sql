--
-- PostgreSQL database dump
--

-- Dumped from database version 14.1
-- Dumped by pg_dump version 14.1

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: root; Type: SCHEMA; Schema: -; Owner: root
--

CREATE SCHEMA root;


ALTER SCHEMA root OWNER TO root;

SET default_tablespace = '';

SET default_table_access_method = heap;


--
-- Name: live_offline_log_info; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.live_offline_log_info (
    id bigint NOT NULL,
    tenant_id bigint DEFAULT 0 NOT NULL,
    domain character varying(128) NOT NULL,
    end_time timestamp(0) without time zone NOT NULL,
    max_id bigint,
    type character varying(128) NOT NULL,
    url character varying(500) NOT NULL,
    file_name character varying(512) NOT NULL,
    file_size bigint,
    status smallint NOT NULL,
    creator character varying(64),
    create_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updater character varying(64),
    update_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted smallint DEFAULT 0 NOT NULL
);


ALTER TABLE root.live_offline_log_info OWNER TO root;

--
-- Name: TABLE live_offline_log_info; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.live_offline_log_info IS '直播日志-离线日志信息表';


--
-- Name: COLUMN live_offline_log_info.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.id IS '编号';


--
-- Name: COLUMN live_offline_log_info.tenant_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.tenant_id IS '租户ID';


--
-- Name: COLUMN live_offline_log_info.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.domain IS '域名';


--
-- Name: COLUMN live_offline_log_info.end_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.end_time IS '结束时间';


--
-- Name: COLUMN live_offline_log_info.max_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.max_id IS 'ID最大值';


--
-- Name: COLUMN live_offline_log_info.type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.type IS '日志类型 1、PUSH, 2、PULL';


--
-- Name: COLUMN live_offline_log_info.url; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.url IS '文件下载链接';


--
-- Name: COLUMN live_offline_log_info.file_name; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.file_name IS '文件名称';


--
-- Name: COLUMN live_offline_log_info.file_size; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.file_size IS '文件大小';


--
-- Name: COLUMN live_offline_log_info.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.status IS '文件状态';


--
-- Name: COLUMN live_offline_log_info.creator; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.creator IS '创建者';


--
-- Name: COLUMN live_offline_log_info.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.create_time IS '创建时间';


--
-- Name: COLUMN live_offline_log_info.updater; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.updater IS '更新者';


--
-- Name: COLUMN live_offline_log_info.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.update_time IS '更新时间';


--
-- Name: COLUMN live_offline_log_info.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.deleted IS '是否删除';


--
-- Name: live_offline_log_info_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE SEQUENCE root.live_offline_log_info_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE root.live_offline_log_info_id_seq OWNER TO root;

--
-- Name: live_offline_log_info_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER SEQUENCE root.live_offline_log_info_id_seq OWNED BY root.live_offline_log_info.id;


--
-- Name: live_offline_log_info id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_offline_log_info ALTER COLUMN id SET DEFAULT nextval('root.live_offline_log_info_id_seq'::regclass);


--
-- Data for Name: live_offline_log_info; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.live_offline_log_info (id, tenant_id, domain, end_time, type, url, file_name, file_size, status, creator, create_time, updater, update_time, deleted) FROM stdin;
\.


--
-- Name: live_offline_log_info_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.live_offline_log_info_id_seq', 1, false);


--
-- Name: live_offline_log_info live_offline_log_info_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_offline_log_info
    ADD CONSTRAINT live_offline_log_info_pkey PRIMARY KEY (id);


--
-- Name: live_offline_log_info_idx; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX live_offline_log_info_idx ON root.live_offline_log_info USING btree (tenant_id, domain, type);


--
-- Name: live_log_delivery_record; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.live_log_delivery_record
(
    id              bigint                 NOT NULL,
    tenant_id       bigint   DEFAULT 0     NOT NULL,
    domain          character varying(128) NOT NULL,
    biz_type        character varying(32)  NOT NULL,
    log_start_time  timestamp(0) without time zone NOT NULL,
    log_end_time    timestamp(0) without time zone NOT NULL,
    log_start_id    bigint                 NOT NULL,
    log_end_id      bigint                 NOT NULL,
    log_type        character varying(128) NOT NULL,
    retry_count     smallint DEFAULT 0     NOT NULL,
    actual_count    bigint   DEFAULT 0     NOT NULL,
    delivery_count  bigint   DEFAULT 0     NOT NULL,
    delivery_status smallint DEFAULT 0     NOT NULL,
    delivery_result character varying(1024),
    deleted         smallint DEFAULT 0     NOT NULL,
    create_time     timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time     timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE root.live_log_delivery_record OWNER TO root;

--
-- Name: TABLE live_log_delivery_record; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.live_log_delivery_record IS '直播日志投递记录表';


--
-- Name: COLUMN live_log_delivery_record.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.id IS '自增id';


--
-- Name: COLUMN live_log_delivery_record.tenant_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.tenant_id IS '租户ID';


--
-- Name: COLUMN live_log_delivery_record.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.domain IS '域名';


--
-- Name: COLUMN live_log_delivery_record.biz_type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.biz_type IS '业务类型,云直播：LSS';


--
-- Name: COLUMN live_log_delivery_record.log_start_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_start_time IS '日志开始时间';


--
-- Name: COLUMN live_log_delivery_record.log_end_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_end_time IS '日志结束时间';


--
-- Name: COLUMN live_log_delivery_record.log_start_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_start_id IS '日志开始ID';


--
-- Name: COLUMN live_log_delivery_record.log_end_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_end_id IS '日志结束ID';


--
-- Name: COLUMN live_log_delivery_record.log_type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_type IS '日志类型';


--
-- Name: COLUMN live_log_delivery_record.retry_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.retry_count IS '失败后重试次数，默认值0';


--
-- Name: COLUMN live_log_delivery_record.actual_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.actual_count IS '实际条数';


--
-- Name: COLUMN live_log_delivery_record.delivery_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.delivery_count IS '投递条数';


--
-- Name: COLUMN live_log_delivery_record.delivery_status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.delivery_status IS '日志投递状态 0:初始值，1:成功，3：重试，-1：过期';


--
-- Name: COLUMN live_log_delivery_record.delivery_result; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.delivery_result IS '日志投递返回结果';


--
-- Name: COLUMN live_log_delivery_record.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.deleted IS '是否删除';


--
-- Name: COLUMN live_log_delivery_record.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.create_time IS '创建时间';


--
-- Name: COLUMN live_log_delivery_record.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.update_time IS '更新时间';


--
-- Name: live_log_delivery_record_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE SEQUENCE root.live_log_delivery_record_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE CACHE 1;


ALTER TABLE root.live_log_delivery_record_id_seq OWNER TO root;

--
-- Name: live_log_delivery_record_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER SEQUENCE root.live_log_delivery_record_id_seq OWNED BY root.live_log_delivery_record.id;

--
-- Name: live_log_delivery_record_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.live_log_delivery_record_id_seq', 1, false);


--
-- Name: live_log_delivery_record live_log_delivery_record_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_log_delivery_record
    ADD CONSTRAINT live_log_delivery_record_pkey PRIMARY KEY (id);


--
-- Name: live_log_delivery_record_idx; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX live_log_delivery_record_idx ON root.live_log_delivery_record USING btree (tenant_id, domain, biz_type, log_type);


--
-- Name: live_log_delivery_record id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_log_delivery_record ALTER COLUMN id SET DEFAULT nextval('root.live_log_delivery_record_id_seq'::regclass);


--
-- Name: live_log_delivery_detail; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.live_log_delivery_detail
(
    id          bigint                 NOT NULL,
    domain      character varying(128) NOT NULL,
    log_id      bigint                 NOT NULL,
    log_time    timestamp(0) without time zone NOT NULL,
    status      smallint               NOT NULL,
    create_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE root.live_log_delivery_detail OWNER TO root;

--
-- Name: TABLE live_log_delivery_detail; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.live_log_delivery_detail IS '直播日志投递明细表';


--
-- Name: COLUMN live_log_delivery_record.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.id IS '自增id';


--
-- Name: COLUMN live_log_delivery_detail.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.domain IS '域名';


--
-- Name: COLUMN live_log_delivery_detail.log_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.log_id IS '日志ID';


--
-- Name: COLUMN live_log_delivery_detail.log_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.log_time IS '日志打印时间, ISO 8601标准格式';


--
-- Name: COLUMN live_log_delivery_detail.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.status IS '日志投递状态 1:成功 2:失败 4：延迟补传';


--
-- Name: COLUMN live_log_delivery_detail.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.create_time IS '创建时间';


--
-- Name: COLUMN live_log_delivery_detail.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.update_time IS '更新时间';


--
-- Name: live_log_delivery_detail live_log_delivery_detail_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_log_delivery_detail
    ADD CONSTRAINT live_log_delivery_detail_pkey PRIMARY KEY (id);


--
-- Name: live_log_delivery_detail_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE SEQUENCE root.live_log_delivery_detail_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE CACHE 1;


ALTER TABLE root.live_log_delivery_detail_id_seq OWNER TO root;

--
-- Name: live_log_delivery_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER SEQUENCE root.live_log_delivery_detail_id_seq OWNED BY root.live_log_delivery_detail.id;

--
-- Name: live_log_delivery_detail_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.live_log_delivery_detail_id_seq', 1, false);

CREATE INDEX live_log_delivery_detail_create_time_domain_idx ON root.live_log_delivery_detail USING BTREE (create_time, domain);

CREATE INDEX live_log_delivery_detail_log_time_domain_idx ON root.live_log_delivery_detail USING BTREE (log_time, domain);


--
-- Name: cdn_log_delivery_record; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.cdn_log_delivery_record
(
    id              bigint                 NOT NULL,
    tenant_id       bigint   DEFAULT 0     NOT NULL,
    domain          character varying(128) NOT NULL,
    biz_type        character varying(32)  NOT NULL,
    log_start_time  timestamp(0) without time zone NOT NULL,
    log_end_time    timestamp(0) without time zone NOT NULL,
    log_start_id    bigint                 NOT NULL,
    log_end_id      bigint                 NOT NULL,
    log_type        character varying(128) NOT NULL,
    retry_count     smallint DEFAULT 0     NOT NULL,
    actual_count    bigint   DEFAULT 0     NOT NULL,
    delivery_count  bigint   DEFAULT 0     NOT NULL,
    delivery_status smallint DEFAULT 0     NOT NULL,
    delivery_result character varying(1024),
    deleted         smallint DEFAULT 0     NOT NULL,
    create_time     timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time     timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE root.cdn_log_delivery_record OWNER TO root;

--
-- Name: TABLE cdn_log_delivery_record; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.cdn_log_delivery_record IS '通用CDN日志投递记录表';


--
-- Name: COLUMN cdn_log_delivery_record.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.id IS '自增id';


--
-- Name: COLUMN cdn_log_delivery_record.tenant_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.tenant_id IS '租户ID';


--
-- Name: COLUMN cdn_log_delivery_record.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.domain IS '域名';


--
-- Name: COLUMN cdn_log_delivery_record.biz_type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.biz_type IS '业务类型,通用CDN：CDN';


--
-- Name: COLUMN cdn_log_delivery_record.log_start_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_start_time IS '日志开始时间';


--
-- Name: COLUMN cdn_log_delivery_record.log_end_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_end_time IS '日志结束时间';


--
-- Name: COLUMN cdn_log_delivery_record.log_start_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_start_id IS '日志开始ID';


--
-- Name: COLUMN cdn_log_delivery_record.log_end_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_end_id IS '日志结束ID';


--
-- Name: COLUMN cdn_log_delivery_record.log_type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_type IS '日志类型';


--
-- Name: COLUMN cdn_log_delivery_record.retry_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.retry_count IS '失败后重试次数，默认值0';


--
-- Name: COLUMN cdn_log_delivery_record.actual_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.actual_count IS '实际条数';


--
-- Name: COLUMN cdn_log_delivery_record.delivery_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.delivery_count IS '投递条数';


--
-- Name: COLUMN cdn_log_delivery_record.delivery_status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.delivery_status IS '日志投递状态 0:初始值，1:成功，3：重试，-1：过期';


--
-- Name: COLUMN cdn_log_delivery_record.delivery_result; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.delivery_result IS '日志投递返回结果';


--
-- Name: COLUMN cdn_log_delivery_record.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.deleted IS '是否删除';


--
-- Name: COLUMN cdn_log_delivery_record.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.create_time IS '创建时间';


--
-- Name: COLUMN cdn_log_delivery_record.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.update_time IS '更新时间';


--
-- Name: cdn_log_delivery_record_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE SEQUENCE root.cdn_log_delivery_record_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE CACHE 1;


ALTER TABLE root.cdn_log_delivery_record_id_seq OWNER TO root;

--
-- Name: cdn_log_delivery_record_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER SEQUENCE root.cdn_log_delivery_record_id_seq OWNED BY root.cdn_log_delivery_record.id;

--
-- Name: cdn_log_delivery_record_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.cdn_log_delivery_record_id_seq', 1, false);


--
-- Name: cdn_log_delivery_record cdn_log_delivery_record_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.cdn_log_delivery_record
    ADD CONSTRAINT cdn_log_delivery_record_pkey PRIMARY KEY (id);


--
-- Name: cdn_log_delivery_record_idx; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX cdn_log_delivery_record_idx ON root.cdn_log_delivery_record USING btree (tenant_id, domain, biz_type, log_type);


--
-- Name: cdn_log_delivery_record id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.cdn_log_delivery_record ALTER COLUMN id SET DEFAULT nextval('root.cdn_log_delivery_record_id_seq'::regclass);


--
-- Name: cdn_log_delivery_detail; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.cdn_log_delivery_detail
(
    id          bigint                 NOT NULL,
    domain      character varying(128) NOT NULL,
    log_id      bigint                 NOT NULL,
    log_time    timestamp(0) without time zone NOT NULL,
    status      smallint               NOT NULL,
    create_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE root.cdn_log_delivery_detail OWNER TO root;

--
-- Name: TABLE cdn_log_delivery_detail; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.cdn_log_delivery_detail IS '通用CDN日志投递明细表';


--
-- Name: COLUMN cdn_log_delivery_record.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.id IS '自增id';


--
-- Name: COLUMN cdn_log_delivery_detail.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.domain IS '域名';


--
-- Name: COLUMN cdn_log_delivery_detail.log_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.log_id IS '日志ID';


--
-- Name: COLUMN cdn_log_delivery_detail.log_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.log_time IS '日志打印时间, ISO 8601标准格式';


--
-- Name: COLUMN cdn_log_delivery_detail.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.status IS '日志投递状态 1:成功 2:失败 4：延迟补传';


--
-- Name: COLUMN cdn_log_delivery_detail.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.create_time IS '创建时间';


--
-- Name: COLUMN cdn_log_delivery_detail.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.update_time IS '更新时间';


--
-- Name: cdn_log_delivery_detail cdn_log_delivery_detail_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.cdn_log_delivery_detail
    ADD CONSTRAINT cdn_log_delivery_detail_pkey PRIMARY KEY (id);


--
-- Name: cdn_log_delivery_detail_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE SEQUENCE root.cdn_log_delivery_detail_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE CACHE 1;


ALTER TABLE root.cdn_log_delivery_detail_id_seq OWNER TO root;

--
-- Name: cdn_log_delivery_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER SEQUENCE root.cdn_log_delivery_detail_id_seq OWNED BY root.cdn_log_delivery_detail.id;

--
-- Name: cdn_log_delivery_detail_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.cdn_log_delivery_detail_id_seq', 1, false);

CREATE INDEX cdn_log_delivery_detail_create_time_domain_idx ON root.cdn_log_delivery_detail USING BTREE (create_time, domain);

CREATE INDEX cdn_log_delivery_detail_log_time_domain_idx ON root.cdn_log_delivery_detail USING BTREE (log_time, domain);