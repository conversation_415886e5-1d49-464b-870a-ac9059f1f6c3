package com.nspace.group.module.infra.service.offlinelog.vendor;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nspace.group.module.infra.convert.vendor.offlinelog.VendorOfflineLogProcessRecordConvert;
import com.nspace.group.module.infra.dal.dataobject.offlinelog.vendor.VendorOfflineLogProcessRecordDO;
import com.nspace.group.module.infra.dal.mapper.offlinelog.vendor.VendorOfflineLogProcessRecordMapper;
import com.nspace.group.module.infra.enums.offlinelog.OfflineLogStatusEnum;
import com.nspace.group.module.infra.service.offlinelog.vendor.dto.VendorOfflineLogProcessRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 第三方离线日志处理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VendorOfflineLogProcessRecordServiceImpl implements VendorOfflineLogProcessRecordService {

    @Autowired
    VendorOfflineLogProcessRecordMapper logProcessRecordMapper;


    @Override
    public VendorOfflineLogProcessRecordDTO getNextProcessRecord(Long tenantId, String domain, String platform, Integer interval, Integer offset, Long startTimestamp) {
        QueryWrapper<VendorOfflineLogProcessRecordDO> queryWrapper = new QueryWrapper<>();
        String startTimeExp = String.format("MAX(start_time) + INTERVAL '%s' MINUTE AS start_time", offset);
        queryWrapper.select("tenant_id", "domain", "platform", startTimeExp)
                .eq("tenant_id", tenantId).eq("domain", domain)
                .eq("platform", platform).eq("deleted", 0)
                .groupBy("tenant_id", "domain", "platform");

        VendorOfflineLogProcessRecordDTO processRecord = VendorOfflineLogProcessRecordConvert.INSTANCE.getProcessRecordDTO(logProcessRecordMapper.selectOne(queryWrapper));
        return processRecord != null ? processRecord : VendorOfflineLogProcessRecordConvert.INSTANCE.newProcessRecordDTO(tenantId, domain, platform, interval, startTimestamp);
    }

    @Override
    public List<VendorOfflineLogProcessRecordDTO> getFailedProcessRecords(Long tenantId, String domain, String platform) {
        List<VendorOfflineLogProcessRecordDO> dos = logProcessRecordMapper.selectList(new LambdaQueryWrapper<VendorOfflineLogProcessRecordDO>()
                .select(VendorOfflineLogProcessRecordDO::getId, VendorOfflineLogProcessRecordDO::getStartTime, VendorOfflineLogProcessRecordDO::getStatus)
                .eq(VendorOfflineLogProcessRecordDO::getTenantId, tenantId).eq(VendorOfflineLogProcessRecordDO::getDomain, domain)
                .eq(VendorOfflineLogProcessRecordDO::getPlatform, platform).eq(VendorOfflineLogProcessRecordDO::getDeleted, 0)
                .eq(VendorOfflineLogProcessRecordDO::getStatus, OfflineLogStatusEnum.FAILED.getStatus()));
        return VendorOfflineLogProcessRecordConvert.INSTANCE.getProcessRecordDTOList(dos);
    }

    @Override
    public void saveLogInfos(List<VendorOfflineLogProcessRecordDTO> processRecords) {
        Map<Boolean, List<VendorOfflineLogProcessRecordDO>> insertUpdateDOListMap = processRecords.stream()
                .map(VendorOfflineLogProcessRecordConvert.INSTANCE::getProcessRecordDO)
                .collect(Collectors.partitioningBy(logInfo -> Objects.isNull(logInfo.getId())));
        List<VendorOfflineLogProcessRecordDO> insertLogInfoDOS = insertUpdateDOListMap.get(Boolean.TRUE);
        List<VendorOfflineLogProcessRecordDO> updateLogInfoDOS = insertUpdateDOListMap.get(Boolean.FALSE);
        if (!insertLogInfoDOS.isEmpty()) {
            logProcessRecordMapper.insertBatch(insertLogInfoDOS);
        }
        if (!updateLogInfoDOS.isEmpty()) {
            logProcessRecordMapper.updateBatch(updateLogInfoDOS, 1000);
        }
        log.info("saveLogInfos,insert_count={},update_count={}", insertLogInfoDOS.size(), updateLogInfoDOS.size());
    }

    @Override
    public void saveProcessRecord(VendorOfflineLogProcessRecordDTO processRecord) {
        VendorOfflineLogProcessRecordDO processRecordDO = VendorOfflineLogProcessRecordConvert.INSTANCE.getProcessRecordDO(processRecord);
        logProcessRecordMapper.insertOrUpdate(processRecordDO);
    }
}
