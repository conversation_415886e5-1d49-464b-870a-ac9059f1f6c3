package com.nspace.group.module.infra.service.offlinelog.vendor.dto;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :VendorOfflineLogProcessRecordDTO.java, v0.1 2025年01月07日 18:23 zhangxin Exp
 */
public class VendorOfflineLogProcessRecordDTO {
    /**
     * ID
     */
    private Long id;

    /**
     * 多租户编号
     */
    private Long tenantId;

    /**
     * 域名
     */
    private String domain;

    /**
     * 三方平台code
     */
    private String platform;

    /**
     * 开始时间点
     */
    private LocalDateTime startTime;

    /**
     * 状态 0、正常；1、失败；
     */
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
}
