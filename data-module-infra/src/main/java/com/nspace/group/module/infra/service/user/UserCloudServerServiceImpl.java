package com.nspace.group.module.infra.service.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.nspace.group.framework.common.pojo.PageParam;
import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.module.infra.constant.RedisKeyConstants;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveDomainDO;
import com.nspace.group.module.infra.dal.mapper.user.UserCloudServerMapper;
import com.nspace.group.module.infra.enums.user.UserCloudServerTypeEnum;
import com.nspace.group.module.infra.enums.user.UserCloudServiceTypeEnum;
import com.nspace.group.module.infra.service.user.dto.LogDeliveryDomainDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户开通的云服务相关配置
 *
 * @author：<EMAIL>
 * @date： 2024/12/16
 * @time：17:32
 */
@Slf4j
@Service
public class UserCloudServerServiceImpl implements UserCloudServerService {

    @Resource
    private UserCloudServerMapper userCloudServerMapper;

    @Override
    public PageResult<LiveDomainDO> getUserCloudDomainList(PageParam pageInfo, String cloudType, String domainType) {

        Page<LiveDomainDO> page = new Page<>(pageInfo.getPageNo(), pageInfo.getPageSize());

        UserCloudServerTypeEnum typeEnum = UserCloudServerTypeEnum.fromCode(cloudType);
        if (typeEnum == null) {
            throw new IllegalArgumentException("Invalid cloud type: " + cloudType);
        }
        Integer statusByCode = UserCloudServerTypeEnum.getStatusByCode(cloudType);
        Page<LiveDomainDO> liveDomainDOS = userCloudServerMapper.selectUserCloudServerWithDomain(domainType, cloudType, statusByCode, page);
        return new PageResult<>(
                liveDomainDOS.getRecords(),
                liveDomainDOS.getTotal()
        );
    }

    @Override
    public PageResult<LogDeliveryDomainDTO> getLogDeliveryDomainList(PageParam pageInfo, String serviceType, String domainType, String deliveryTarget) {
        if (!UserCloudServiceTypeEnum.isUserCloudServiceType(serviceType)) {
            throw new IllegalArgumentException("Invalid cloud service type: " + serviceType);
        }
        if (UserCloudServiceTypeEnum.GYLSS.isSelf(serviceType)) {
            Page<LiveDomainDO> page = PageDTO.of(pageInfo.getPageNo(), pageInfo.getPageSize());
            Page<LogDeliveryDomainDTO> pageData = userCloudServerMapper.selectLiveLogDeliveryDomainList(page, serviceType, domainType, deliveryTarget);
            return new PageResult<>(pageData.getRecords(), pageData.getTotal());
        } else if (UserCloudServiceTypeEnum.GYCDN.isSelf(serviceType)) {
            Page<Object> page = PageDTO.of(pageInfo.getPageNo(), pageInfo.getPageSize());
            Page<LogDeliveryDomainDTO> pageData = userCloudServerMapper.selectCdnLogDeliveryDomainList(page, serviceType, domainType, deliveryTarget);
            return new PageResult<>(pageData.getRecords(), pageData.getTotal());
        } else {
            throw new RuntimeException("unknown user cloud service type");
        }
    }

    @Override
    @Cacheable(value = RedisKeyConstants.LOG_DELIVERY_RECOVERY_KEY, key = "#serviceType + ':' + #deliveryTarget", unless = "#result == null")
    public List<LogDeliveryDomainDTO> getLogDeliveryConfigList(String serviceType, String deliveryTarget) {
        return userCloudServerMapper.selectLogDeliveryConfigList(serviceType, deliveryTarget);
    }
}
