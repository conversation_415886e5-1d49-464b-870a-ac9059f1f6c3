package com.nspace.group.module.infra.service.cloudvendor.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/1
 * @time：11:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VendorAccountWithDomainsDTO {
    /**
     * 账号
     */
    private String account;
    /**
     * paltform
     */
    private String platform;
    /**
     * 秘钥 ID
     */
    private String secretId;

    /**
     * 秘钥 Key
     */
    private String secretKey;

    /**
     * 接入点
     */
    private String endpoint;

    /**
     * 绑定的租户 ID
     */
    private Long bindTenantId;

    /**
     * 对应的域名信息
      */
    private String domain;

    /**
     * 扩展信息
     */
    private String extInfo;

}
