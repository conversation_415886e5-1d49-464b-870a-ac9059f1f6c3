package com.nspace.group.module.infra.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户开通的云服务相关配置
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/12/16
 * @time：17:32
 */

@TableName("user_cloud_server")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserCloudServerDO {

    @TableId
    private long id;
    private String userId;
    private String instanceId;
    private String serverType;
    private String name;
    private String code;
    private String orderId;
    private String startTime;
    private String endTime;
    private String status;
    private String enableTimes;
    private String enableRegions;
    private String specName;
    private String specCode;
    private String chargeType;
    private String chargeItem;
    private String chargePeriod;
    private String obtainWay;
    private short sort = 1;
    private String remark;
    private String extInfo;
    private String creator;
    private String createTime;
    private String updater;
    private String updateTime;
    private short deleted = 0;
    private long tenantId = 0;
}
