package com.nspace.group.module.infra.convert.vendor.offlinelog;

import com.nspace.group.module.infra.dal.dataobject.offlinelog.vendor.VendorOfflineLogProcessRecordDO;
import com.nspace.group.module.infra.service.offlinelog.vendor.dto.VendorOfflineLogProcessRecordDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

/**
 * <AUTHOR>
 * @version :VendorOfflineLogProcessRecordConvert.java, v0.1 2025年01月07日 10:21 zhangxin Exp
 */
@Mapper
public interface VendorOfflineLogProcessRecordConvert {

    VendorOfflineLogProcessRecordConvert INSTANCE = Mappers.getMapper(VendorOfflineLogProcessRecordConvert.class);

    VendorOfflineLogProcessRecordDTO getProcessRecordDTO(VendorOfflineLogProcessRecordDO processRecordDO);

    List<VendorOfflineLogProcessRecordDTO> getProcessRecordDTOList(List<VendorOfflineLogProcessRecordDO> processRecordDOList);

    @Mapping(target = "deleted", ignore = true)
    VendorOfflineLogProcessRecordDO getProcessRecordDO(VendorOfflineLogProcessRecordDTO dto);

    @Mapping(target = "status", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "startTime", expression = "java(generateStartTime(interval, startTimestamp))")
    VendorOfflineLogProcessRecordDTO newProcessRecordDTO(Long tenantId, String domain, String platform, Integer interval, Long startTimestamp);

    /**
     * 为新域名生成开始时间
     *
     * @param interval  时间间隔
     * @param startTimestamp 配置的开始时间戳
     * @return 开始时间
     */
    default LocalDateTime generateStartTime(Integer interval, Long startTimestamp) {
        ZoneOffset defaultOffset = ZoneOffset.ofHours(8);
        LocalDateTime endTime = LocalDateTime.now(defaultOffset).minusHours(3);
        LocalDateTime defaultStartTime = endTime.minusHours(interval);
        if (null == startTimestamp) {
            return defaultStartTime;
        }
        //如果配置的开始时间不在默认开始时间之前则依旧使用默认的
        LocalDateTime configuredStartTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(startTimestamp), defaultOffset);
        return configuredStartTime.isBefore(defaultStartTime) ? configuredStartTime : defaultStartTime;
    }
}