package com.nspace.group.module.infra.convert.vendor.offlinelog;

import com.nspace.group.module.infra.dal.dataobject.offlinelog.vendor.VendorOfflineLogInfoDO;
import com.nspace.group.module.infra.service.offlinelog.vendor.dto.VendorOfflineLogInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version :VendorOfflineLogInfoConvert.java, v0.1 2024年12月05日 10:21 zhangxin Exp
 */
@Mapper
public interface VendorOfflineLogInfoConvert {

    VendorOfflineLogInfoConvert INSTANCE = Mappers.getMapper(VendorOfflineLogInfoConvert.class);

    VendorOfflineLogInfoDTO getOfflineLogInfoDTO(VendorOfflineLogInfoDO logInfoDO);

    List<VendorOfflineLogInfoDTO> getOfflineLogInfoDTOList(List<VendorOfflineLogInfoDO> logInfoDOList);

    @Mapping(target = "deleted", ignore = true)
    VendorOfflineLogInfoDO getOfflineLogInfoDO(VendorOfflineLogInfoDTO dto);
}