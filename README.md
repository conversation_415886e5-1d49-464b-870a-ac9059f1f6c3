# 工程介绍

data-works 项目的主要目的是构建一套数据基础设施，提供从数据采集到数据分析的全链路支持。进行以下几项任务： 
* 数据融合与接入：从多个数据源（如数据库、API、文件等）采集数据。
* 数据处理与转换：将第三方数据统一处理成内部需要标准格式。

# 工程模块介绍

| 项目                   | 说明        |
|----------------------|-----------|
| data-works-framework | 基础组件拓展    |
| data-works-server    | 项目启动类     |
| data-works-scheduler | 异步或定时任务定义 |
| data-module-fusion   | 第三方数据融合模块 |
| data-module-infra    | 基础设施的模块   |
| data-module-log      | 离线日志的模块   |

# 任务管理
## 常用任务定义
| 状态  | 任务名称                      | handlerName                      | handlerParam                                                     | cronExpression  | retryCount |
|-----|---------------------------|----------------------------------|------------------------------------------------------------------|-----------------|------------|
| 在用  | 数据融合-实时监控-推流流量带宽/推流质量监控   | fusionRealtimeStreamPushInfoJob  | {\"timeout\":7200000,\"interval\":20,\"domain_type\":\"PUSH\"}   | */10 * * * * ?  | 3          |
| 在用  | 计费用量-直播拉流带宽和流量统计          | fusionUsagePullBandwidthFluxJob  | {\"timeout\":7200000,\"interval\":3600,\"domain_type\":\"PULL\"} | 0 */5 * * * ?   | 3          |
| 在用  | 计费用量-直播推流带宽和流量统计          | fusionUsagePushBandwidthFluxJob  | {\"timeout\":7200000,\"interval\":3600,\"domain_type\":\"PUSH\"} | 0 */5 * * * ?   | 3          |
| 已停止 | 应用监控-kafka connect应用监控和重启 | kafkaConnectMonitorAndRestartJob |                                                                  | 0 * * * * ?     | 3          |
| 在用  | 离线日志-七牛云离线日志拉取            | fusionOfflineLogJob              | {\"interval\":3,\"offset\":10,\"start\":1741693687}              | 0 */10 * * * ?  | 3          |
| 暂定  | 离线日志-直播离线日志生成             | offlineLogGenerateJob            | {\"interval\":5}                                                 | 0 */5 * * * ?   | 3          |
| 已停止 | 日志投递-京东直播CDN实时日志          | jdLiveLogDeliveryJob             | {\"delayOffset\":60}                                             | 0 * * * * ?     | 3          |
| 已停止 | 日志投递-JD相关-直播拉流延迟日志补发      | jdLiveDelayedLogDeliveryJob      |                                                                  | 0 * * * * ?     | 3          |
| 暂定  | 日志投递-Ali相关-通用CDN实时日志      | aliCdnLogDeliveryJob             | {\"delayOffset\":60}                                             | 10 * * * * ?    | 3          |
| 暂定  | 日志投递-Ali相关-通用CDN延迟日志补发    | aliCdnDelayedLogDeliveryJob      |                                                                  | 10 * * * * ?    | 3          |
| 在用  | 第三方日志-直播CDN-重新消费          | vendorLiveLogRecoveryJob         | {\"limit\":10000}                                                | 20 * * * * ?    | 3          |
| 在用  | 第三方日志-通用CDN-重新消费          | vendorCdnLogRecoveryJob          | {\"limit\":10000}                                                | 20 * * * * ?    | 3          |
| 在用  | 直播失败日志补发-耕耘日志投递给JD        | jdLiveLogRecoveryJob             |                                                                  | 0/10 * * * * ?  | 3          |
| 在用  | 内部应用监控-flink运行任务记录        | flinkJobsOverviewJob             |                                                                  | 0 * * * * ?     | 3          |
| 暂定  | 离线日志-Ali相关-天翼云离线日志拉取      | fusionCtOfflineLogJob            | {\"interval\":3,\"offset\":10,\"start\":1745997481}              | 20 */10 * * * ? | 3          |

## 创建任务

```json
curl -X POST http://127.0.0.1:9081/scheduler/job/create \
-H "Content-Type: application/json" \
-d '{
    "name": "内部应用监控-flink运行任务记录",
    "handlerName": "flinkJobsOverviewJob",
    "handlerParam":"",
    "cronExpression": "0 * * * * ?",
    "retryCount": 3,
    "retryInterval": 1000,
    "monitorTimeout": 1000
}'
```
## 更新任务
```json
curl -X PUT http://127.0.0.1:9081/scheduler/job/update \
-H "Content-Type: application/json" \
-d '{
    "id": 4,
    "name": " 计费用量-直播推带宽和流量统计",
    "handlerName": "fusionUsagePushBandwidthFluxJob",
    "handlerParam":"{\"timeout\":7200000,\"interval\":3600,\"domain_type\":\"PUSH\"}",
    "cronExpression": "0 */5 * * * ?",
    "retryCount": 3,
    "retryInterval": 1000,
    "monitorTimeout": 1000
}'
```

