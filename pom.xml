<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.nspace.group</groupId>
    <artifactId>data-works</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>data-works</name>
    <description>
        data-works 数据综合处理平台
        1:数据融合
    </description>
    <modules>
        <module>data-works-server</module>
        <module>data-works-framework</module>
        <module>data-module-fusion</module>
        <module>data-module-infra</module>
        <module>data-works-scheduler</module>
        <module>data-module-log</module>
        <module>data-module-application</module>
    </modules>
    <properties>
        <java.version>11</java.version>
        <revision>1.0.0-SNAPSHOT</revision>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <spring.boot.version>2.7.17</spring.boot.version>
        <!-- Maven 相关 -->
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <!-- 消息队列 -->
        <kafka-spring.version>2.8.11</kafka-spring.version>
        <!-- DB 相关 -->
        <druid.version>1.2.20</druid.version>
        <mybatis-plus.version>3.5.4</mybatis-plus.version>
        <dynamic-datasource.version>3.6.1</dynamic-datasource.version>
        <mybatis-plus-join.version>1.4.7</mybatis-plus-join.version>
        <redisson.version>3.18.0</redisson.version>
        <dm8.jdbc.version>8.1.3.62</dm8.jdbc.version>
        <postgresql.version>42.6.0</postgresql.version>
        <!-- 工具类相关 -->
        <captcha-plus.version>1.0.10</captcha-plus.version>
        <jsoup.version>1.16.2</jsoup.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <lombok.mapstruct.binding.version>0.2.0</lombok.mapstruct.binding.version>
        <hutool.version>5.8.22</hutool.version>
        <velocity.version>2.3</velocity.version>
        <screw.version>1.0.5</screw.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>32.1.3-jre</guava.version>
        <guice.version>5.1.0</guice.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <tika-core.version>2.9.1</tika-core.version>
        <ip2region.version>2.7.0</ip2region.version>
        <ipdb.version>1.1.3</ipdb.version>
        <maxmind-db.version>3.1.0</maxmind-db.version>
        <apache.httpclient.version>4.5.3</apache.httpclient.version>
        <!-- 监控相关 -->
        <skywalking.version>8.12.0</skywalking.version>
        <!-- 第三方sdk相关 -->
        <tencentcloud-sdk-java.version>3.1.1130</tencentcloud-sdk-java.version>
        <minio-sdk-java.version>8.5.14</minio-sdk-java.version>
        <swagger.version>2.2.8</swagger.version>
        <aliyun-log-producer.version>0.3.25</aliyun-log-producer.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 业务组件 -->
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-spring-boot-starter-banner</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-spring-boot-starter-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-spring-boot-starter-redis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-spring-boot-starter-job</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-spring-boot-starter-file</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-module-infra</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-module-fusion-tencent</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-module-fusion-qiniu</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-module-fusion-ct</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-module-log</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-works-scheduler</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-module-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- 工具类相关 -->
            <dependency>
                <groupId>com.nspace.group</groupId>
                <artifactId>data-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>net.ipip</groupId>
                <artifactId>ipdb</artifactId>
                <version>${ipdb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.maxmind.db</groupId>
                <artifactId>maxmind-db</artifactId>
                <version>${maxmind-db.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId> <!-- 文件类型的识别 -->
                <version>${tika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${guice.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <!-- 消息队列相关 -->
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${kafka-spring.version}</version>
            </dependency>
            <!-- DB 相关 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId> <!-- 提供数据库连接池、SQL监控、性能监控等功能。它适用于管理和监控数据库连接的性能-->
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter
                </artifactId> <!-- MyBatis-Plus 是 MyBatis 的增强工具，简化了 CRUD 操作并提供了更高效的开发工具。这是数据库操作层的核心依赖，适合用于简化数据表的 CRUD 代码 -->
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter
                </artifactId> <!-- 于自动生成数据库实体类、DAO、Mapper 接口等。适合用于项目初期自动化代码生成，并不涉及数据库管理或连接池功能 -->
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter
                </artifactId> <!-- 其支持联表查询。与 mybatis-plus-boot-starter 功能互补，不涉及连接池、数据源管理或代码生成 -->
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId> <!-- 基于 Redis 实现的分布式锁。它提供了分布式锁、分布式队列等功能。适合于分布式环境下的并发控制 -->
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm8.jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
                <scope>compile</scope>
            </dependency>
            <!-- 监控相关 -->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <!-- 第三方sdk相关 -->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencentcloud-sdk-java.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio-sdk-java.version}</version>
            </dependency>
            <!-- 七牛云 sdk -->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>7.17.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${apache.httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>aliyun-log-producer</artifactId>
                <version>${aliyun-log-producer.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <!-- lombok和mapstruct一起使用需要此配置 -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <!-- Lombok 1.18.16 及以上需要此配置 -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok.mapstruct.binding.version}</version>
                        </path>
                        <!-- Mapstruct跟在lombok后面 -->
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>