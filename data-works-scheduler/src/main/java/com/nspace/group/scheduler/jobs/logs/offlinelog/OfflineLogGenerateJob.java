package com.nspace.group.scheduler.jobs.logs.offlinelog;


import cn.hutool.json.JSONObject;
import com.nspace.group.framework.common.pojo.PageParam;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveDomainDO;
import com.nspace.group.module.infra.enums.user.UserCloudServerTypeEnum;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.user.UserCloudServerService;
import com.nspace.group.module.logs.convert.offlinelog.OfflineLogInfoConvert;
import com.nspace.group.module.logs.service.offlinelog.OfflineLogFileService;
import com.nspace.group.module.logs.service.offlinelog.OfflineLogGenerateService;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogLiveDomainDTO;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * 离线日志生成
 */

@Slf4j
@Component
public class OfflineLogGenerateJob extends AbstractFusionJob {

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Resource
    private OfflineLogGenerateService logGenerateService;

    @Resource
    private OfflineLogFileService logFileService;

    @Resource
    private UserCloudServerService userCloudServerService;

    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
    }

    @Override
    public String execute(String param) throws Exception {
        JSONObject object = validateAndParseParam(param);
        if (object == null) {
            return "params_error";
        }


        List<OfflineLogLiveDomainDTO> tenantDomains = getAccountDomains();
        if (tenantDomains.isEmpty()) {
            log.warn("no_tenant_domain_provided,no_op");
            return "vendor account is empty";
        }
        Integer interval = object.getInt("interval");

        //依次根据tenant_id创建bucket
//        tenantDomains.stream()
//                .map(OfflineLogLiveDomainDTO::getTenantId)
//                .distinct()
//                .forEach(tenantId -> logFileService.checkCreateBucket(tenantId.toString()));

        tenantDomains.forEach(tenantDomain ->
                executorService.execute(() -> {
                    String tenantId = tenantDomain.getTenantId().toString();
                    String domain = tenantDomain.getDomain();
                    String type = tenantDomain.getType();
                    log.info("[offlineLogGenerateJob_execute],before_execute,tenantId={},domain={},type={},param={}", tenantId, domain, type, param);
                    logGenerateService.generateOfflineLog(tenantDomain, interval);
                    log.info("[offlineLogGenerateJob_execute],finished_execute,tenantId={},domain={},type={},param={}", tenantId, domain, type, param);
                })
        );
        return "success";
    }

    private List<OfflineLogLiveDomainDTO> getAccountDomains() {
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(PageParam.PAGE_SIZE_NONE);

        List<LiveDomainDO> tenantDomainDOList = userCloudServerService.getUserCloudDomainList(pageParam,
                UserCloudServerTypeEnum.OFFLINE_LOG.getCode(), null).getList();
        return OfflineLogInfoConvert.INSTANCE.getLiveDomainDTOList(tenantDomainDOList);
    }
}
