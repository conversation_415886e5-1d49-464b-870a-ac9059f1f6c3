package com.nspace.group.scheduler.jobs.logs.delivery;


import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import com.nspace.group.framework.common.pojo.PageParam;
import com.nspace.group.module.infra.enums.user.UserCloudServiceTypeEnum;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.user.UserCloudServerService;
import com.nspace.group.module.infra.service.user.dto.LogDeliveryDomainDTO;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryConvert;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import com.nspace.group.module.logs.service.delivery.LogDeliveryService;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryParamDTO;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * JD直播CDN延迟日志投递
 */

@Slf4j
@Component
public class JdLiveDelayedLogDeliveryJob extends AbstractFusionJob {

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Resource
    private LogDeliveryService logDeliveryService;

    @Resource
    private UserCloudServerService userCloudServerService;

    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
    }

    @Override
    public String execute(String param) {
        List<LogDeliveryParamDTO> deliveryParams = getTenantDomainDeliveryParams();

        if (deliveryParams.isEmpty()) {
            log.warn("no_tenant_domain_provided,no_op");
            return "vendor account is empty";
        }

        deliveryParams.forEach(tenantDomainParam ->
                executorService.execute(() -> {
                    String tenantId = tenantDomainParam.getTenantId().toString();
                    String domain = tenantDomainParam.getDomain();
                    log.info("[jdLiveDelayedLogDeliveryJob_execute],before_execute,tenantId={},domain={}", tenantId, domain);
                    logDeliveryService.deliverDelayedLog(tenantDomainParam);
                    log.info("[jdLiveDelayedLogDeliveryJob__execute],finished_execute,tenantId={},domain={}", tenantId, domain);
                })
        );
        return "success";
    }

    private List<LogDeliveryParamDTO> getTenantDomainDeliveryParams() {
        String targetType = LogDeliveryTargetEnum.LIVE_JD.getName();
        Integer batchLimit = 10000;
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LogDeliveryDomainDTO> liveDomains = userCloudServerService.getLogDeliveryDomainList(pageParam,
                UserCloudServiceTypeEnum.GYLSS.getCode(), LiveDomainTypeEnum.DOMAIN_TYPE_PULL.getCode(), LogDeliveryTargetEnum.LIVE_JD.getVendorCode()).getList();
        return liveDomains.stream()
                .map(logDeliveryDomain -> LogDeliveryConvert.INSTANCE.getLogDeliveryParamDTO(
                        logDeliveryDomain, BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), targetType, batchLimit, 0))
                .collect(Collectors.toList());
    }
}
