package com.nspace.group.scheduler.jobs.logs.delivery;


import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.pojo.PageParam;
import com.nspace.group.module.infra.enums.user.UserCloudServiceTypeEnum;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.user.UserCloudServerService;
import com.nspace.group.module.infra.service.user.dto.LogDeliveryDomainDTO;
import com.nspace.group.module.logs.biz.delivery.DeliveryLogRecoveryService;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryConvert;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogRecoveryConfigDTO;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * JD直播CDN实时日志投递
 */

@Slf4j
@Component
public class JdLiveLogRecoveryJob extends AbstractFusionJob {

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Resource
    private DeliveryLogRecoveryService logRecoveryService;

    @Resource
    private UserCloudServerService userCloudServerService;

    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
    }

    @Override
    public String execute(String param) {
        Collection<DeliveryLogRecoveryConfigDTO> deliveryParams = getDeliveryLogRecoveryParams();

        if (deliveryParams.isEmpty()) {
            log.warn("no_delivery_config_provided,no_op");
            return "user cloud server is empty";
        }

        deliveryParams.forEach(deliveryParam ->
                executorService.execute(() -> {
                    log.info("[jdLiveLogRecoveryJob_execute],before_execute,target={}", deliveryParam.getTargetType());
                    logRecoveryService.recoverLogs(deliveryParam);
                    log.info("[jdLiveLogRecoveryJob_execute],finished_execute,target={}", deliveryParam.getTargetType());
                })
        );
        return "success";
    }

    private Collection<DeliveryLogRecoveryConfigDTO> getDeliveryLogRecoveryParams() {
        String targetType = LogDeliveryTargetEnum.LIVE_JD.getName();
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LogDeliveryDomainDTO> liveDomains = userCloudServerService.getLogDeliveryConfigList(UserCloudServiceTypeEnum.GYLSS.getCode(), LogDeliveryTargetEnum.LIVE_JD.getVendorCode());
        Map<String, DeliveryLogRecoveryConfigDTO> vendorTargetConfigMap = liveDomains.stream()
                .map(logDeliveryDomain -> LogDeliveryConvert.INSTANCE.getDeliveryLogRecoveryConfigDTO(
                        logDeliveryDomain, BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), targetType, null))
                .collect(Collectors.toMap(DeliveryLogRecoveryConfigDTO::getTargetType, deliveryConfig -> deliveryConfig, (v1, v2) -> v1));
        return vendorTargetConfigMap.values();
    }
}
