package com.nspace.group.scheduler.jobs.application;

import com.nspace.group.framework.job.core.handler.JobHandler;
import com.nspace.group.module.application.service.KafkaConnectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * kafka connect 监控重启任务
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2025/1/7
 * @time：20:25
 */
@Slf4j
@Component
public class KafkaConnectMonitorAndRestartJob implements JobHandler{

    @Resource
    private KafkaConnectService kafkaConnectService;
    /**
     * 执行任务
     *
     * @param param 参数
     * @return 结果
     * @throws Exception 异常
     */
    @Override
    public String execute(String param) throws Exception {
        kafkaConnectService.monitorAndRestartConnectors();
        return param;
    }
}
