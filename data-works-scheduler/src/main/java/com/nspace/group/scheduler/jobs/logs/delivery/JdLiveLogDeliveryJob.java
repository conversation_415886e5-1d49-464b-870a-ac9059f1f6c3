package com.nspace.group.scheduler.jobs.logs.delivery;


import cn.hutool.json.JSONObject;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import com.nspace.group.framework.common.pojo.PageParam;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.infra.enums.user.UserCloudServiceTypeEnum;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.user.UserCloudServerService;
import com.nspace.group.module.infra.service.user.dto.LogDeliveryDomainDTO;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryConvert;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import com.nspace.group.module.logs.service.delivery.LogDeliveryService;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryParamDTO;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * JD直播CDN实时日志投递
 */

@Slf4j
@Component
public class JdLiveLogDeliveryJob extends AbstractFusionJob {

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Resource
    private LogDeliveryService logDeliveryService;

    @Resource
    private UserCloudServerService userCloudServerService;

    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
    }

    @Override
    public String execute(String param) {
        JSONObject object = JsonUtils.parseObject(param, JSONObject.class);
        Integer delayOffset = null;
        if (object != null) {
            delayOffset = object.getInt("delayOffset");
        }

        List<LogDeliveryParamDTO> deliveryParams = getTenantDomainDeliveryParams(delayOffset);

        if (deliveryParams.isEmpty()) {
            log.warn("no_tenant_domain_provided,no_op");
            return "vendor account is empty";
        }

        deliveryParams.forEach(tenantDomainParam ->
                executorService.execute(() -> {
                    String tenantId = tenantDomainParam.getTenantId().toString();
                    String domain = tenantDomainParam.getDomain();
                    log.info("[jdLiveLogDeliveryJob_execute],before_execute,tenantId={},domain={}", tenantId, domain);
                    logDeliveryService.deliverLog(tenantDomainParam);
                    log.info("[jdLiveLogDeliveryJob_execute],finished_execute,tenantId={},domain={}", tenantId, domain);
                })
        );
        return "success";
    }

    private List<LogDeliveryParamDTO> getTenantDomainDeliveryParams(Integer delayOffset) {
        String targetType = LogDeliveryTargetEnum.LIVE_JD.getName();
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LogDeliveryDomainDTO> liveDomains = userCloudServerService.getLogDeliveryDomainList(pageParam,
                UserCloudServiceTypeEnum.GYLSS.getCode(), LiveDomainTypeEnum.DOMAIN_TYPE_PULL.getCode(), LogDeliveryTargetEnum.LIVE_JD.getVendorCode()).getList();
        return liveDomains.stream()
                .map(logDeliveryDomain -> LogDeliveryConvert.INSTANCE.getLogDeliveryParamDTO(
                        logDeliveryDomain, BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), targetType, null, delayOffset))
                .collect(Collectors.toList());
    }
}
