package com.nspace.group.scheduler.jobs.fusion.offlinelog;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.fusion.ct.service.OfflineLogService;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 离线日志拉取
 */

@Slf4j
@Component
public class FusionCtOfflineLogJob extends AbstractFusionJob {

    @Resource(name = "ctOfflineLogService")
    OfflineLogService offlineLogService;

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
    }

    @Override
    public String execute(String param) throws Exception {
        JSONObject object = validateAndParseParam(param);
        if (object == null) {
            return "params_error";
        }

        List<VendorAccountWithDomainsDTO> accountWithDomains = getTenantDomainParams();
        if (accountWithDomains.isEmpty()) {
            log.warn("no_vendor_account_provided,no_op");
            return "vendor account is empty";
        }
        Integer interval = object.getInt("interval");
        Integer offset = object.getInt("offset");
        Long startTimestamp = object.getLong("start");

        accountWithDomains.forEach(accountWithDomain ->
                executorService.execute(() -> {
                    Long tenantId = accountWithDomain.getBindTenantId();
                    String domain = accountWithDomain.getDomain();
                    log.info("[FusionCtOfflineLogJob_execute],before_execute,tenantId={},domain={},param={}", tenantId, domain, param);
                    offlineLogService.processOfflineLog(accountWithDomain, interval, offset, startTimestamp);
                    log.info("[FusionCtOfflineLogJob_execute],finished_execute,tenantId={},domain={},param={}", tenantId, domain, param);
                })
        );
        return "success";
    }

    private List<VendorAccountWithDomainsDTO> getTenantDomainParams() {
        String platform = DataPlatformEnum.PLATFORM_CTYUN.getCode().toLowerCase();
        List<VendorAccountWithDomainsDTO> vendorDomains = vendorAccountService.getVendorAccountDomains(Arrays.asList(platform),
                BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), null);
        return vendorDomains.stream()
                .filter(accountDomain -> {
                    String extInfo = accountDomain.getExtInfo();
                    return StrUtil.isNotBlank(extInfo)
                            && JsonUtils.parseTree(extInfo).path("vendor_offline_log").asBoolean();
                }).collect(Collectors.toList());
    }
}
