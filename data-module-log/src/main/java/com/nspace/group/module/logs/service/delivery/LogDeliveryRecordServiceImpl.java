package com.nspace.group.module.logs.service.delivery;

import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryParamDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 日志投递记录接口实现类
 *
 * <AUTHOR>
 * @since 2025-03-18 14:32:16
 */
@Service
@Validated
public class LogDeliveryRecordServiceImpl implements LogDeliveryRecordService {

    @Resource
    LiveLogDeliveryRecordService liveLogDeliveryRecordService;

    @Resource
    CdnLogDeliveryRecordService cdnLogDeliveryRecordService;

    @Override
    public LogDeliveryRecordDTO getCurrentDeliveryRecord(LogDeliveryParamDTO paramDTO) {
        String bizType = paramDTO.getBizType();
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
           return liveLogDeliveryRecordService.getCurrentDeliveryRecord(paramDTO);
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            return cdnLogDeliveryRecordService.getCurrentDeliveryRecord(paramDTO);
        }
        throw new RuntimeException("unsupported biz_type=" + bizType);
    }

    @Override
    public void save(LogDeliveryRecordDTO deliveryRecord) {
        String bizType = deliveryRecord.getBizType();
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            liveLogDeliveryRecordService.save(deliveryRecord);
            return;
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            cdnLogDeliveryRecordService.save(deliveryRecord);
            return;
        }
        throw new RuntimeException("unsupported biz_type=" + bizType);
    }
}

