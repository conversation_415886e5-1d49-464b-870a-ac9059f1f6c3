package com.nspace.group.module.logs.service.offlinelog.dto;


/**
 * OfflineLogLiveDomainDTO
 *
 * <AUTHOR>
 */
public class OfflineLogLiveDomainDTO {

    /**
     * 多租户编号
     */
    private Long tenantId;
    /**
     * 用户侧域名
     */
    private String domain;

    /**
     * 域名类型 PUSH、推流；PULL、播流
     */
    private String type;

    /**
     * 状态 0、正常；1、停用；
     */
    private Integer status;

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
