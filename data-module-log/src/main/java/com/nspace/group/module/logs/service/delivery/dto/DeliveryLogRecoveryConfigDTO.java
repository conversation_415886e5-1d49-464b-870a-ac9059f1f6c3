package com.nspace.group.module.logs.service.delivery.dto;

import cn.hutool.core.lang.Assert;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;

/**
 * 日志投递恢复查询参数DTO
 *
 * <AUTHOR>
 */
public class DeliveryLogRecoveryConfigDTO {

    private final String bizType;

    // 日志投递目标
    private final String targetType;

    // 单次投递数量限制
    private final Integer logLimit;

    // 批次查询限制
    private final Integer batchLimit;

    // 重试次数
    private final Integer retryCount;

    // 双方约定的唯一标识
    private final String privateKey;
    // ak
    private final String accessKey;

    // 接口path
    private final String apiPath;

    // 接口基础Url
    private final String apiBaseUrl;


    public DeliveryLogRecoveryConfigDTO(String bizType, String targetType,
                                        Integer logLimit, Integer batchLimit,
                                        Integer retryCount, String privateKey, String accessKey,
                                        String apiPath, String apiBaseUrl) {
        Assert.notBlank(targetType);
        Assert.notBlank(privateKey);
        Assert.notBlank(accessKey);
        Assert.notBlank(apiPath);
        Assert.notBlank(apiBaseUrl);
        Assert.isTrue(LogDeliveryTargetEnum.isLogDeliveryTarget(targetType));
        this.bizType = bizType;
        this.targetType = targetType;
        this.logLimit = logLimit != null && logLimit > 0 ? logLimit : 1000;
        this.batchLimit = batchLimit != null && batchLimit > 0 ? batchLimit : 10000;
        this.retryCount = retryCount != null && retryCount > 0 ? retryCount : 15;
        this.privateKey = privateKey;
        this.accessKey = accessKey;
        this.apiPath = apiPath;
        this.apiBaseUrl = apiBaseUrl;
    }

    public String getTargetType() {
        return targetType;
    }

    public Integer getLogLimit() {
        return logLimit;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public String getApiPath() {
        return apiPath;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    public Integer getBatchLimit() {
        return batchLimit;
    }

    public String getBizType() {
        return bizType;
    }

    public Integer getRetryCount() {
        return retryCount;
    }
}
