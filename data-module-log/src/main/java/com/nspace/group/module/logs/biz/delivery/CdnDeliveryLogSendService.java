package com.nspace.group.module.logs.biz.delivery;

import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryConfigDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :CdnDeliveryLogSendService.java, v0.1 2025年03月31日 15:30 Exp
 */
public interface CdnDeliveryLogSendService {

    /**
     * 投递原始日志数据
     *
     * @param logData      压缩后的日志数据
     * @param logIdTimeMap 日志ID时间map
     * @param taskKey      投递任务锁
     * @return 投递结果
     */
    Object send(LogDeliveryConfigDTO deliveryConfig, List<Map<String, Object>> logData, Map<Long, LocalDateTime> logIdTimeMap, String taskKey);
}
