package com.nspace.group.module.logs.convert.offlinelog;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;
import com.nspace.group.module.logs.service.offlinelog.dto.LiveStreamPullDetailDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.LiveStreamPushDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :OfflineLogConvert.java, v0.1 2024年12月17日 10:21 zhangxin Exp
 */
@Mapper
public interface OfflineLogConvert {

    DateTimeFormatter logTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssxxx");

    OfflineLogConvert INSTANCE = Mappers.getMapper(OfflineLogConvert.class);
    JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .defaultDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();


    @Mapping(target = "via", ignore = true)
    @Mapping(target = "hostname", ignore = true)
    @Mapping(target = "videoGap", source = "videoMaxGapMs")
    @Mapping(target = "userPin", ignore = true)
    @Mapping(target = "uri", expression = "java(cn.hutool.core.util.StrUtil.isNotBlank(pushDO.getRewriteUri()) ? pushDO.getRewriteUri() : pushDO.getRequestUri())")
    @Mapping(target = "upstreamResponseTime", ignore = true)
    @Mapping(target = "upstreamResponseCode", ignore = true)
    @Mapping(target = "upstreamHeaderTime", ignore = true)
    @Mapping(target = "upstreamFirstByteTime", ignore = true)
    @Mapping(target = "upstreamConnectTime", ignore = true)
    @Mapping(target = "upstreamBytesReceived", ignore = true)
    @Mapping(target = "traceId", source = "requestId")
    @Mapping(target = "totalSentBytes", source = "bytesSent")
    @Mapping(target = "timeIso8601", expression = "java(pushDO.getLogTime().toInstant().atOffset(java.time.ZoneOffset.ofHours(8)).format(logTimeFormatter))")
    @Mapping(target = "streamStatus", expression = "java(String.valueOf(pushDO.getRound() == 0 ? -1 : pushDO.getEnd()))")
    @Mapping(target = "requestTime", source = "duration")
    @Mapping(target = "requestMethod", constant = "publish")
    @Mapping(target = "remoteAddr", source = "clientAddr")
    @Mapping(target = "msec", expression = "java(pushDO.getLogTime().toInstant().toEpochMilli())")
    @Mapping(target = "httpUserAgent", source = "userAgent")
    @Mapping(target = "httpReferer", source = "referer")
    @Mapping(target = "host", source = "domain")
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "sourceStreamFps", expression = "java(Integer.valueOf(0).equals(pushDO.getSourceStreamFps()) ? pushDO.getVideoFps().doubleValue() : pushDO.getSourceStreamFps().doubleValue())")
    @Mapping(target = "framesSent", ignore = true)
    @Mapping(target = "framesReceived", expression = "java(pushDO.getVideoFps() * pushDO.getDuration())")
    @Mapping(target = "firstGopSentTime", source = "firstGopSentTimeMs")
    @Mapping(target = "errorDiscription", source = "err")
    @Mapping(target = "chargeBy", ignore = true)
    @Mapping(target = "cacheLevel", ignore = true)
    @Mapping(target = "bytesReceived", source = "bytesRecv")
    @Mapping(target = "avgGopSize", source = "avgGopSizeMs")
    @Mapping(target = "audiosFpsWarn", ignore = true)
    @Mapping(target = "audioFramesSent", constant = "0L")
    @Mapping(target = "audioFramesReceived", constant = "0L")
    @Mapping(target = "connectTime", expression = "java(Long.valueOf(pushDO.getConnectTime().toInstant().toEpochMilli()).doubleValue())")
    LiveStreamPushDetailDTO getStreamPushLogDTO(OdsBillingLogStreamPushDO pushDO);

    List<LiveStreamPushDetailDTO> getStreamPushLogDTOList(List<OdsBillingLogStreamPushDO> pushDOList);

    @Mapping(target = "via", ignore = true)
    @Mapping(target = "hostname", ignore = true)
    @Mapping(target = "videoGap", source = "videoMaxGapMs")
    @Mapping(target = "userPin", ignore = true)
    @Mapping(target = "uri", expression = "java(cn.hutool.core.util.StrUtil.isNotBlank(pullDO.getRewriteUri()) ? pullDO.getRewriteUri() : pullDO.getRequestUri())")
    @Mapping(target = "upstreamResponseTime", ignore = true)
    @Mapping(target = "upstreamResponseCode", ignore = true)
    @Mapping(target = "upstreamHeaderTime", ignore = true)
    @Mapping(target = "upstreamFirstByteTime", ignore = true)
    @Mapping(target = "upstreamConnectTime", ignore = true)
    @Mapping(target = "upstreamBytesReceived", ignore = true)
    @Mapping(target = "traceId", source = "requestId")
    @Mapping(target = "totalSentBytes", source = "bytesSent")
    @Mapping(target = "timeIso8601", expression = "java(pullDO.getLogTime().toInstant().atOffset(java.time.ZoneOffset.ofHours(8)).format(logTimeFormatter))")
    @Mapping(target = "streamStatus", expression = "java(String.valueOf(pullDO.getRound() == 0 ? -1 : pullDO.getEnd()))")
    @Mapping(target = "requestTime", source = "duration")
    @Mapping(target = "requestMethod", constant = "publish")
    @Mapping(target = "remoteAddr", source = "clientAddr")
    @Mapping(target = "msec", expression = "java(pullDO.getLogTime().toInstant().toEpochMilli())")
    @Mapping(target = "httpUserAgent", source = "userAgent")
    @Mapping(target = "httpReferer", source = "referer")
    @Mapping(target = "host", source = "domain")
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "sourceStreamFps", expression = "java(Integer.valueOf(0).equals(pullDO.getSourceStreamFps()) ? pullDO.getVideoFps().doubleValue() : pullDO.getSourceStreamFps().doubleValue())")
    @Mapping(target = "framesSent", ignore = true)
    @Mapping(target = "framesReceived", expression = "java(pullDO.getVideoFps() * pullDO.getDuration())")
    @Mapping(target = "firstGopSentTime", source = "firstGopSentTimeMs")
    @Mapping(target = "errorDiscription", source = "err")
    @Mapping(target = "chargeBy", ignore = true)
    @Mapping(target = "cacheLevel", ignore = true)
    @Mapping(target = "bytesReceived", source = "bytesRecv")
    @Mapping(target = "avgGopSize", source = "avgGopSizeMs")
    @Mapping(target = "audiosFpsWarn", ignore = true)
    @Mapping(target = "audioFramesSent", constant = "0L")
    @Mapping(target = "audioFramesReceived", constant = "0L")
    @Mapping(target = "connectTime", expression = "java(Long.valueOf(pullDO.getConnectTime().toInstant().toEpochMilli()).doubleValue())")
    LiveStreamPullDetailDTO getStreamPullLogDTO(OdsBillingLogStreamPullDO pullDO);

    List<LiveStreamPullDetailDTO> getStreamPullLogDTOList(List<OdsBillingLogStreamPullDO> pullDOList);

    default List<Map<String, String>> getPushLogMapList(List<LiveStreamPushDetailDTO> pushLogDTOList) {
        TypeFactory typeFactory = jsonMapper.getTypeFactory();
        TypeReference<HashMap<String, String>> typeRef = new TypeReference<>() {};
        JavaType javaType = typeFactory.constructType(typeRef);
        return jsonMapper.convertValue(pushLogDTOList, typeFactory.constructCollectionType(List.class, javaType));
    }

    default List<Map<String, String>> getPullLogMapList(List<LiveStreamPullDetailDTO> pullLogDTOList) {
        TypeFactory typeFactory = jsonMapper.getTypeFactory();
        TypeReference<HashMap<String, String>> typeRef = new TypeReference<>() {};
        JavaType javaType = typeFactory.constructType(typeRef);
        return jsonMapper.convertValue(pullLogDTOList, typeFactory.constructCollectionType(List.class, javaType));
    }
}
