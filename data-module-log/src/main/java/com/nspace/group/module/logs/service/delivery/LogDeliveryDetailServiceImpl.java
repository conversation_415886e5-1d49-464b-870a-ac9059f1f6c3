package com.nspace.group.module.logs.service.delivery;

import cn.hutool.core.collection.CollectionUtil;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryDetailDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 日志投递详情接口实现类
 *
 * <AUTHOR>
 * @since 2025-04-10 14:32:16
 */
@Service
@Validated
public class LogDeliveryDetailServiceImpl implements LogDeliveryDetailService {

    @Resource
    LiveLogDeliveryDetailService liveLogDeliveryDetailService;

    @Resource
    CdnLogDeliveryDetailService cdnLogDeliveryDetailService;

    @Override
    public List<LogDeliveryDetailDTO> getDetails(String bizType, String domain, Integer batchLimit,
                                                 LocalDateTime timeLowerLimit, Long curMaxId) {
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            return liveLogDeliveryDetailService.getDetails(domain, batchLimit, timeLowerLimit, curMaxId);
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            return cdnLogDeliveryDetailService.getDetails(domain, batchLimit, timeLowerLimit, curMaxId);
        }
        throw new RuntimeException("unsupported biz_type=" + bizType);
    }

    @Override
    public void updateMany(String bizType, Set<Long> deliveryDetailIds, Integer deliveryStatus) {
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            liveLogDeliveryDetailService.updateMany(deliveryDetailIds, deliveryStatus);
            return;
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            cdnLogDeliveryDetailService.updateMany(deliveryDetailIds, deliveryStatus);
            return;
        }
        throw new RuntimeException("unsupported biz_type=" + bizType);
    }

    @Override
    public void saveMany(String bizType, String domain, Integer deliveryStatus, Map<Long, LocalDateTime> logIdTimeMap) {
        if (CollectionUtil.isEmpty(logIdTimeMap)) return;
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            liveLogDeliveryDetailService.saveMany(domain, deliveryStatus, logIdTimeMap);
            return;
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            cdnLogDeliveryDetailService.saveMany(domain, deliveryStatus, logIdTimeMap);
            return;
        }
        throw new RuntimeException("unsupported biz_type=" + bizType);
    }
}
