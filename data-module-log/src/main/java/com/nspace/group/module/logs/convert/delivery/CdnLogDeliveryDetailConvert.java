package com.nspace.group.module.logs.convert.delivery;

import com.nspace.group.module.logs.dal.dataobject.delivery.CdnLogDeliveryDetailDO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version :CdnLogDeliveryDetailConvert.java, v0.1 2025年04月10日 10:21 zhangxin Exp
 */
@Mapper
public interface CdnLogDeliveryDetailConvert {

    CdnLogDeliveryDetailConvert INSTANCE = Mappers.getMapper(CdnLogDeliveryDetailConvert.class);

    @Mapping(target = "updateTime", ignore = true)
    CdnLogDeliveryDetailDO getDetailDO(LogDeliveryDetailDTO detailDTO);

    List<CdnLogDeliveryDetailDO> getDetailDOList(List<LogDeliveryDetailDTO> detailDTOList);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    LogDeliveryDetailDTO newDetailDTO(String domain, Long logId, LocalDateTime logTime, Integer status);

    LogDeliveryDetailDTO getDeliveryDetailDTO(CdnLogDeliveryDetailDO detailDO);

    List<LogDeliveryDetailDTO> getDeliveryDetailDTOList(List<CdnLogDeliveryDetailDO> detailDOS);
}
