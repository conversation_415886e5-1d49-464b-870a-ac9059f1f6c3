package com.nspace.group.module.logs.biz.delivery;

import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CdnDeliveryLogSendServiceImpl implements CdnDeliveryLogSendService {

    @Resource
    private JdCdnDeliveryLogSendService jdCdnDeliveryLogSendService;

    @Resource
    private AliCdnDeliveryLogSendService aliCdnDeliveryLogSendService;

    @Override
    public Object send(LogDeliveryConfigDTO deliveryConfig, List<Map<String, Object>> logData, Map<Long, LocalDateTime> logIdTimeMap, String taskKey) {
        String targetType = deliveryConfig.getTargetType();
        if (LogDeliveryTargetEnum.CDN_JD.isSelf(targetType)) {
            return jdCdnDeliveryLogSendService.send(deliveryConfig, logData);
        } else if (LogDeliveryTargetEnum.CDN_ALI.isSelf(targetType)) {
            return aliCdnDeliveryLogSendService.send(deliveryConfig, logData, logIdTimeMap, taskKey);
        }
        throw new RuntimeException("unknown_log_delivery_target,target=" + targetType);
    }
}
