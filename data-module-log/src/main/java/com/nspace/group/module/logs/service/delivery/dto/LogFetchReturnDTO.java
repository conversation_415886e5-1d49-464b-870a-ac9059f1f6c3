package com.nspace.group.module.logs.service.delivery.dto;

import java.util.List;
import java.util.Map;

/**
 * 日志查询返回DTO
 *
 * <AUTHOR>
 */
public class LogFetchReturnDTO {

    // 日志数据
    private final List<Map<String, Object>> logList;

    public LogFetchReturnDTO(List<Map<String, Object>> logList) {
        this.logList = logList;
    }

    public List<Map<String, Object>> getLogList() {
        return logList;
    }

}
