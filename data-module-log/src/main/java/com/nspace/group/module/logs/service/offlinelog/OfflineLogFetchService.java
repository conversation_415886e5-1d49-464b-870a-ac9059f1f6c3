package com.nspace.group.module.logs.service.offlinelog;


import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.LiveStreamPullDetailDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.LiveStreamPushDetailDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version :OfflineLogFetchService.java, v0.1 2024年12月19日 11:52 Exp
 */
public interface OfflineLogFetchService {

    /**
     * 获取推流离线日志数据
     *
     * @param reqDTO 请求参数
     * @return List<LiveStreamPushDetailDTO>
     */
    List<LiveStreamPushDetailDTO> fetchPushOfflineLog(StreamDetailReqDTO reqDTO);

    /**
     * 获取播流离线日志数据
     *
     * @param reqDTO 请求参数
     * @return List<LiveStreamPullDetailDTO>
     */
    List<LiveStreamPullDetailDTO> fetchPullOfflineLog(StreamDetailReqDTO reqDTO);
}
