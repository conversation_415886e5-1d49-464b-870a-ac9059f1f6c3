package com.nspace.group.module.logs.service.offlinelog.dto;

import java.time.LocalDateTime;

/**
 * OfflineLogFileSaveDTO
 *
 * <AUTHOR>
 */
public class OfflineLogFileSaveDTO {

    //编号
    private Long id;

    //租户ID
    private String tenantId;

    //域名
    private String domain;

    //结束时间点
    private LocalDateTime endTime;

    //日志类型
    private String type;

    //文件名
    private String name;

    //存储bucket
    private String bucket;

    //存储器侧路径和名称
    private String path;

    //文件大小
    private Long size;

    //文件内容
    private byte[] content;

    //文件上传flag
    private Integer status;

    public Long getId() {
        return id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}