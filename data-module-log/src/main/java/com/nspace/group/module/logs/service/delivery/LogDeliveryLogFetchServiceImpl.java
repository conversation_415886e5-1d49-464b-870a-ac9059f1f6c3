package com.nspace.group.module.logs.service.delivery;

import com.google.common.collect.Lists;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import com.nspace.group.module.infra.dal.dataobject.cdn.OdsGeneralCdnRequestLogDO;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;
import com.nspace.group.module.infra.service.cdn.OdsGeneralCdnRequestLogService;
import com.nspace.group.module.infra.service.cdn.dto.GeneralCdnRequestLogReqDTO;
import com.nspace.group.module.infra.service.detail.OdsBillingLogStreamPullService;
import com.nspace.group.module.infra.service.detail.OdsBillingLogStreamPushService;
import com.nspace.group.module.infra.service.detail.dto.LiveCdnLogReqDTO;
import com.nspace.group.module.logs.biz.delivery.CdnDeliveryLogTransformService;
import com.nspace.group.module.logs.biz.delivery.LiveDeliveryLogTransformService;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryConvert;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryRecordConvert;
import com.nspace.group.module.logs.service.delivery.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :LogDeliveryLogFetchServiceImpl.java, v0.1 2025年03月19日 11:52 Exp
 */
@Service
@Slf4j
public class LogDeliveryLogFetchServiceImpl implements LogDeliveryLogFetchService {

    @Resource
    private OdsGeneralCdnRequestLogService cdnRequestLogService;

    @Resource
    private OdsBillingLogStreamPushService livePushLogService;

    @Resource
    private OdsBillingLogStreamPullService livePullLogService;

    @Resource
    private CdnDeliveryLogTransformService cdnDeliveredLogTransformService;

    @Resource
    private LiveDeliveryLogTransformService liveDeliveredLogTransformService;

    @Override
    public List<LogDeliveryDelayedTaskDTO> fetchDelayedLogs(LogDeliveryDelayedConfigDTO delayedConfigDTO) {

        List<LogDeliveryDetailDTO> deliveryDetails = delayedConfigDTO.getDeliveryDetails();
        List<Long> logIds = deliveryDetails.stream().map(LogDeliveryDetailDTO::getLogId).collect(Collectors.toList());
        Long tenantId = delayedConfigDTO.getTenantId();
        String domain = delayedConfigDTO.getDomain();
        String bizType = delayedConfigDTO.getBizType();
        String logType = delayedConfigDTO.getType();
        Integer logLimit = delayedConfigDTO.getLogLimit();
        String targetType = delayedConfigDTO.getTargetType();
        if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            List<OdsGeneralCdnRequestLogDO> rawLogList = cdnRequestLogService.getDelayedRequestLogList(logIds);
            return Lists.partition(rawLogList, logLimit)
                    .stream()
                    .map(subList -> {
                        Set<Long> deliveryDetailIds = new HashSet<>();
                        List<OdsGeneralCdnRequestLogDO> sortedSubList = subList.stream()
                                .peek(rawLog -> deliveryDetails.stream().filter(deliveryDetail -> deliveryDetail.getLogId()
                                                .equals(rawLog.getId())).map(LogDeliveryDetailDTO::getId)
                                        .collect(Collectors.toCollection(() -> deliveryDetailIds)))
                                .sorted(Comparator.comparing(OdsGeneralCdnRequestLogDO::getLogTime))
                                .collect(Collectors.toList());
                        OdsGeneralCdnRequestLogDO startRequestLog = sortedSubList.get(0);
                        OdsGeneralCdnRequestLogDO endRequestLog = sortedSubList.get(sortedSubList.size() - 1);
                        //第一次同步或新同步任务
                        //设置当前请求log_id[上下]边界与log_time[上下]边界，[]代表包含
                        Long logStartId = startRequestLog.getId();
                        Long logEndId = endRequestLog.getId();
                        LogDeliveryRecordDTO recordForBatch = LogDeliveryRecordConvert.INSTANCE.getRecordDTOForBatch(
                                tenantId, domain, bizType, logType, logStartId, logEndId,
                                startRequestLog.getLogTime(), endRequestLog.getLogTime(), (long) subList.size());
                        List<Map<String, Object>> transformedLogBatch = cdnDeliveredLogTransformService.transformLogs(targetType, subList);
                        return new LogDeliveryDelayedTaskDTO(transformedLogBatch, recordForBatch, deliveryDetailIds);
                    }).collect(Collectors.toList());
        } else if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            ZoneOffset zoneOffset = ZoneOffset.ofHours(8);
            if (LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.isSelf(logType)) {
                List<OdsBillingLogStreamPushDO> pushRawLogList = livePushLogService.getDelayedPushLogList(logIds);
                return Lists.partition(pushRawLogList, logLimit)
                        .stream()
                        .map(subList -> {
                            Set<Long> deliveryDetailIds = new HashSet<>();
                            List<OdsBillingLogStreamPushDO> sortedSubList = subList.stream()
                                    .peek(rawLog -> deliveryDetails.stream().filter(deliveryDetail -> deliveryDetail.getLogId()
                                                    .equals(rawLog.getId())).map(LogDeliveryDetailDTO::getId)
                                            .collect(Collectors.toCollection(() -> deliveryDetailIds)))
                                    .sorted(Comparator.comparing(OdsBillingLogStreamPushDO::getLogTime)).collect(Collectors.toList());
                            OdsBillingLogStreamPushDO startPushLog = sortedSubList.get(0);
                            OdsBillingLogStreamPushDO endPushLog = sortedSubList.get(sortedSubList.size() - 1);
                            //第一次同步或新同步任务
                            //设置当前请求log_id[上下]边界与log_time[上下]边界，[]代表包含
                            Long logStartId = startPushLog.getId();
                            Long logEndId = endPushLog.getId();
                            Date startDate = startPushLog.getLogTime();
                            Date endDate = endPushLog.getLogTime();
                            LocalDateTime logStartTime = LocalDateTime.ofInstant(startDate.toInstant(), zoneOffset);
                            LocalDateTime logEndTime = LocalDateTime.ofInstant(endDate.toInstant(), zoneOffset);
                            LogDeliveryRecordDTO recordForBatch = LogDeliveryRecordConvert.INSTANCE.getRecordDTOForBatch(
                                    tenantId, domain, bizType, logType, logStartId, logEndId, logStartTime, logEndTime, (long) subList.size());

                            List<Map<String, Object>> transformedLogBatch = liveDeliveredLogTransformService.transformPushLogs(targetType, subList);
                            return new LogDeliveryDelayedTaskDTO(transformedLogBatch, recordForBatch, deliveryDetailIds);
                        }).collect(Collectors.toList());
            } else {
                List<OdsBillingLogStreamPullDO> pullRawLogList = livePullLogService.getDelayedPullLogList(logIds);
                return Lists.partition(pullRawLogList, logLimit)
                        .stream()
                        .map(subList -> {
                            Set<Long> deliveryDetailIds = new HashSet<>();
                            List<OdsBillingLogStreamPullDO> sortedSubList = subList.stream()
                                    .peek(rawLog -> deliveryDetails.stream().filter(deliveryDetail -> deliveryDetail.getLogId()
                                                    .equals(rawLog.getId())).map(LogDeliveryDetailDTO::getId)
                                            .collect(Collectors.toCollection(() -> deliveryDetailIds)))
                                    .sorted(Comparator.comparing(OdsBillingLogStreamPullDO::getLogTime)).collect(Collectors.toList());
                            OdsBillingLogStreamPullDO startPullLog = sortedSubList.get(0);
                            OdsBillingLogStreamPullDO endPullLog = sortedSubList.get(sortedSubList.size() - 1);
                            //第一次同步或新同步任务
                            //设置当前请求log_id[上下]边界与log_time[上下]边界，[]代表包含
                            Long logStartId = startPullLog.getId();
                            Long logEndId = endPullLog.getId();
                            Date startDate = startPullLog.getLogTime();
                            Date endDate = endPullLog.getLogTime();
                            LocalDateTime logStartTime = LocalDateTime.ofInstant(startDate.toInstant(), zoneOffset);
                            LocalDateTime logEndTime = LocalDateTime.ofInstant(endDate.toInstant(), zoneOffset);
                            LogDeliveryRecordDTO recordForBatch = LogDeliveryRecordConvert.INSTANCE.getRecordDTOForBatch(
                                    tenantId, domain, bizType, logType, logStartId, logEndId, logStartTime, logEndTime, (long) subList.size());

                            List<Map<String, Object>> transformedLogBatch = liveDeliveredLogTransformService.transformPullLogs(targetType, subList);
                            return new LogDeliveryDelayedTaskDTO(transformedLogBatch, recordForBatch, deliveryDetailIds);
                        }).collect(Collectors.toList());
            }
        } else {
            log.error("fetchDelayedLogs,biz_type_unsupported,biz_type={}", bizType);
            throw new RuntimeException("fetchDelayedLogs_operation_not_supported");
        }
    }

    @Override
    public LogDeliveryTasksRunDTO fetchLogBatch(LogDeliveryConfigDTO deliveryConfig) {
        LogDeliveryRecordDTO deliveryRecord = deliveryConfig.getDeliveryRecord();
        Long tenantId = deliveryRecord.getTenantId();
        String domain = deliveryRecord.getDomain();
        String bizType = deliveryRecord.getBizType();
        String logType = deliveryRecord.getLogType();
        Integer logLimit = deliveryConfig.getLogLimit();
        Integer batchLimit = deliveryConfig.getBatchLimit();
        LocalDateTime timeUpperLimit = deliveryConfig.getTimeUpperLimit();
        String targetType = deliveryConfig.getTargetType();
        if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            GeneralCdnRequestLogReqDTO requestLogReqDTO = LogDeliveryConvert.INSTANCE.getRequestLogReqDTO(deliveryRecord, batchLimit, timeUpperLimit);
            LocalDateTime logMaxTime = null;
            List<OdsGeneralCdnRequestLogDO> rawLogList = cdnRequestLogService.getRequestLogList(requestLogReqDTO);
            AtomicInteger totalLogCount = new AtomicInteger();
            List<LogDeliveryTaskDTO> deliveryTasks = Lists.partition(rawLogList, logLimit)
                    .stream()
                    .peek(sublist -> totalLogCount.addAndGet(sublist.size()))
                    .map(subList -> {
                        OdsGeneralCdnRequestLogDO startRequestLog = subList.get(0);
                        OdsGeneralCdnRequestLogDO endRequestLog = subList.get(subList.size() - 1);
                        //第一次同步或新同步任务
                        //设置当前请求log_id[上下]边界与log_time[上下]边界，[]代表包含
                        Long logStartId = startRequestLog.getId();
                        Long logEndId = endRequestLog.getId();
                        LogDeliveryRecordDTO recordForBatch = LogDeliveryRecordConvert.INSTANCE.getRecordDTOForBatch(
                                tenantId, domain, bizType, logType, logStartId, logEndId,
                                startRequestLog.getLogTime(), endRequestLog.getLogTime(), (long) subList.size());

                        Map<Long, LocalDateTime> logIdTimeMap = subList.stream()
                                .collect(Collectors.toMap(OdsGeneralCdnRequestLogDO::getId,
                                        OdsGeneralCdnRequestLogDO::getLogTime, (k1, k2) -> k1));
                        List<Map<String, Object>> transformedLogBatch = cdnDeliveredLogTransformService.transformLogs(targetType, subList);
                        return new LogDeliveryTaskDTO(transformedLogBatch, recordForBatch, logIdTimeMap);
                    }).collect(Collectors.toList());
            if (!rawLogList.isEmpty()) {
                OdsGeneralCdnRequestLogDO lastRequestLog = rawLogList.get(rawLogList.size() - 1);
                logMaxTime = lastRequestLog.getLogTime();
            }
            return new LogDeliveryTasksRunDTO(deliveryTasks, totalLogCount.get(), logMaxTime);
        } else if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            LiveCdnLogReqDTO reqDTO = LogDeliveryConvert.INSTANCE.getLiveCdnLogReqDTO(deliveryRecord, batchLimit, timeUpperLimit);
            ZoneOffset zoneOffset = ZoneOffset.ofHours(8);
            if (LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.isSelf(logType)) {
                List<OdsBillingLogStreamPushDO> pushRawLogList = livePushLogService.getPushLogList(reqDTO);
                LocalDateTime logMaxTime = null;
                AtomicInteger totalLogCount = new AtomicInteger();
                List<LogDeliveryTaskDTO> deliveryTasks = Lists.partition(pushRawLogList, logLimit)
                        .stream()
                        .peek(sublist -> totalLogCount.addAndGet(sublist.size()))
                        .map(subList -> {
                            OdsBillingLogStreamPushDO startPushLog = subList.get(0);
                            OdsBillingLogStreamPushDO endPushLog = subList.get(subList.size() - 1);
                            //第一次同步或新同步任务
                            //设置当前请求log_id[上下]边界与log_time[上下]边界，[]代表包含
                            Long logStartId = startPushLog.getId();
                            Long logEndId = endPushLog.getId();
                            Date startDate = startPushLog.getLogTime();
                            Date endDate = endPushLog.getLogTime();
                            LocalDateTime logStartTime = LocalDateTime.ofInstant(startDate.toInstant(), zoneOffset);
                            LocalDateTime logEndTime = LocalDateTime.ofInstant(endDate.toInstant(), zoneOffset);
                            LogDeliveryRecordDTO recordForBatch = LogDeliveryRecordConvert.INSTANCE.getRecordDTOForBatch(
                                    tenantId, domain, bizType, logType, logStartId, logEndId, logStartTime, logEndTime, (long) subList.size());

                            Map<Long, LocalDateTime> logIdTimeMap = subList.stream()
                                    .collect(Collectors.toMap(OdsBillingLogStreamPushDO::getId, rawLog -> {
                                        Date logTime = rawLog.getLogTime();
                                        return LocalDateTime.ofInstant(logTime.toInstant(), zoneOffset);
                                    }, (k1, k2) -> k1));
                            List<Map<String, Object>> transformedLogBatch = liveDeliveredLogTransformService.transformPushLogs(targetType, subList);
                            return new LogDeliveryTaskDTO(transformedLogBatch, recordForBatch, logIdTimeMap);
                        }).collect(Collectors.toList());
                if (!pushRawLogList.isEmpty()) {
                    OdsBillingLogStreamPushDO lastPushLog = pushRawLogList.get(pushRawLogList.size() - 1);
                    logMaxTime = LocalDateTime.ofInstant(lastPushLog.getLogTime().toInstant(), zoneOffset);
                }
                return new LogDeliveryTasksRunDTO(deliveryTasks, totalLogCount.get(), logMaxTime);
            } else {
                List<OdsBillingLogStreamPullDO> pullRawLogList = livePullLogService.getPullLogList(reqDTO);
                LocalDateTime logMaxTime = null;
                AtomicInteger totalLogCount = new AtomicInteger();
                List<LogDeliveryTaskDTO> deliveryTasks = Lists.partition(pullRawLogList, logLimit)
                        .stream()
                        .peek(sublist -> totalLogCount.addAndGet(sublist.size()))
                        .map(subList -> {
                            OdsBillingLogStreamPullDO startPullLog = subList.get(0);
                            OdsBillingLogStreamPullDO endPullLog = subList.get(subList.size() - 1);
                            //第一次同步或新同步任务
                            //设置当前请求log_id[上下]边界与log_time[上下]边界，[]代表包含
                            Long logStartId = startPullLog.getId();
                            Long logEndId = endPullLog.getId();
                            Date startDate = startPullLog.getLogTime();
                            Date endDate = endPullLog.getLogTime();
                            LocalDateTime logStartTime = LocalDateTime.ofInstant(startDate.toInstant(), zoneOffset);
                            LocalDateTime logEndTime = LocalDateTime.ofInstant(endDate.toInstant(), zoneOffset);
                            LogDeliveryRecordDTO recordForBatch = LogDeliveryRecordConvert.INSTANCE.getRecordDTOForBatch(
                                    tenantId, domain, bizType, logType, logStartId, logEndId, logStartTime, logEndTime, (long) subList.size());

                            Map<Long, LocalDateTime> logIdTimeMap = subList.stream()
                                    .collect(Collectors.toMap(OdsBillingLogStreamPullDO::getId, rawLog -> {
                                        Date logTime = rawLog.getLogTime();
                                        return LocalDateTime.ofInstant(logTime.toInstant(), zoneOffset);
                                    }, (k1, k2) -> k1));
                            List<Map<String, Object>> transformedLogBatch = liveDeliveredLogTransformService.transformPullLogs(targetType, subList);
                            return new LogDeliveryTaskDTO(transformedLogBatch, recordForBatch, logIdTimeMap);
                        }).collect(Collectors.toList());
                if (!pullRawLogList.isEmpty()) {
                    OdsBillingLogStreamPullDO lastPullLog = pullRawLogList.get(pullRawLogList.size() - 1);
                    logMaxTime = LocalDateTime.ofInstant(lastPullLog.getLogTime().toInstant(), zoneOffset);
                }
                return new LogDeliveryTasksRunDTO(deliveryTasks, totalLogCount.get(), logMaxTime);
            }
        } else {
            log.error("fetchLogBatch,biz_type_unsupported,biz_type={}", bizType);
            throw new RuntimeException("fetchLogBatch_operation_not_supported");
        }
    }
}
