package com.nspace.group.module.logs.service.delivery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.logs.convert.delivery.CdnLogDeliveryDetailConvert;
import com.nspace.group.module.logs.convert.delivery.LiveLogDeliveryDetailConvert;
import com.nspace.group.module.logs.dal.dataobject.delivery.CdnLogDeliveryDetailDO;
import com.nspace.group.module.logs.dal.mapper.delivery.CdnLogDeliveryDetailMapper;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryDetailDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * CDN日志投递详情服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-04-10 14:32:16
 */
@Service
@DS("nspace_log")
@Validated
public class CdnLogDeliveryDetailServiceImpl implements CdnLogDeliveryDetailService {

    @Resource
    CdnLogDeliveryDetailMapper deliveryDetailMapper;

    @Override
    public List<LogDeliveryDetailDTO> getDetails(String domain, Integer batchLimit, LocalDateTime timeLowerLimit, Long curMaxId) {
        LambdaQueryWrapperX<CdnLogDeliveryDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.select(CdnLogDeliveryDetailDO::getId, CdnLogDeliveryDetailDO::getLogId, CdnLogDeliveryDetailDO::getStatus, CdnLogDeliveryDetailDO::getCreateTime)
                .eq(CdnLogDeliveryDetailDO::getDomain, domain).ge(CdnLogDeliveryDetailDO::getCreateTime, timeLowerLimit)
                .ne(CdnLogDeliveryDetailDO::getStatus, LogDeliveryStatusEnum.SUCCESS.getStatus())
                .gt(CdnLogDeliveryDetailDO::getId, curMaxId)
                .orderByAsc(CdnLogDeliveryDetailDO::getId);

        Page<CdnLogDeliveryDetailDO> page = new Page<>(1, batchLimit);
        List<CdnLogDeliveryDetailDO> delayedLogRecords = deliveryDetailMapper.selectPage(page.setSearchCount(false), queryWrapper).getRecords();
        return CdnLogDeliveryDetailConvert.INSTANCE.getDeliveryDetailDTOList(delayedLogRecords);
    }

    @Override
    public void updateMany(Set<Long> deliveryDetailIds, Integer deliveryStatus) {
        if (LogDeliveryStatusEnum.SUCCESS.isSelf(deliveryStatus)) {
            if (!deliveryDetailIds.isEmpty()) {
                LambdaUpdateWrapper<CdnLogDeliveryDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(CdnLogDeliveryDetailDO::getStatus, LogDeliveryStatusEnum.SUCCESS.getStatus())
                        .in(CdnLogDeliveryDetailDO::getId, deliveryDetailIds);
                deliveryDetailMapper.update(updateWrapper);
            }
        }

    }

    @Override
    public void saveMany(String domain, Integer deliveryStatus, Map<Long, LocalDateTime> logIdTimeMap) {
        List<LogDeliveryDetailDTO> deliveryDetails = logIdTimeMap.entrySet().stream()
                .map(entry -> LiveLogDeliveryDetailConvert.INSTANCE.newDetailDTO(domain, entry.getKey(), entry.getValue(), deliveryStatus))
                .collect(Collectors.toList());
        List<CdnLogDeliveryDetailDO> detailDOList = CdnLogDeliveryDetailConvert.INSTANCE.getDetailDOList(deliveryDetails);
        Map<Boolean, List<CdnLogDeliveryDetailDO>> partitions = detailDOList.stream().collect(Collectors.partitioningBy(detailDO -> detailDO.getId() == null));
        List<CdnLogDeliveryDetailDO> updateDetailDOS = partitions.get(Boolean.FALSE);
        deliveryDetailMapper.insertBatch(partitions.get(Boolean.TRUE));
        if (!updateDetailDOS.isEmpty()) {
            deliveryDetailMapper.updateBatch(updateDetailDOS, updateDetailDOS.size());
        }

    }
}

