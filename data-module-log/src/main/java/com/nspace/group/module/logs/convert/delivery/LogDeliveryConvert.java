package com.nspace.group.module.logs.convert.delivery;

import com.nspace.group.module.infra.service.cdn.dto.GeneralCdnRequestLogReqDTO;
import com.nspace.group.module.infra.service.detail.dto.LiveCdnLogReqDTO;
import com.nspace.group.module.infra.service.user.dto.LogDeliveryDomainDTO;
import com.nspace.group.module.logs.service.delivery.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version :LogDeliveryConvert.java, v0.1 2025年03月19日 10:21 zhangxin Exp
 */
@Mapper
public interface LogDeliveryConvert {

    LogDeliveryConvert INSTANCE = Mappers.getMapper(LogDeliveryConvert.class);

    @Mapping(target = "timeUpperLimit", expression = "java(java.time.LocalDateTime.now(java.time.ZoneOffset.ofHours(8)).minusSeconds(paramDTO.getDelayOffset()).truncatedTo(java.time.temporal.ChronoUnit.SECONDS))")
    @Mapping(target = "batchLimit", source = "paramDTO.batchLimit")
    @Mapping(target = "logLimit", source = "paramDTO.limit")
    LogDeliveryConfigDTO getConfigDTO(LogDeliveryParamDTO paramDTO, LogDeliveryRecordDTO deliveryRecord);

    @Mapping(target = "domain", source = "record.domain")
    GeneralCdnRequestLogReqDTO getRequestLogReqDTO(LogDeliveryRecordDTO record, Integer logLimit, LocalDateTime timeUpperLimit);

    @Mapping(target = "domain", source = "record.domain")
    LiveCdnLogReqDTO getLiveCdnLogReqDTO(LogDeliveryRecordDTO record, Integer logLimit, LocalDateTime timeUpperLimit);

    @Mapping(target = "privateKey", source = "deliveryDomain.sk")
    @Mapping(target = "apiPath", source = "deliveryDomain.path")
    @Mapping(target = "apiBaseUrl", source = "deliveryDomain.endpoint")
    @Mapping(target = "accessKey", source = "deliveryDomain.ak")
    LogDeliveryParamDTO getLogDeliveryParamDTO(LogDeliveryDomainDTO deliveryDomain, String bizType, String targetType, Integer batchLimit, Integer delayOffset);

    @Mapping(target = "deliveryRecord", source = "taskRecord")
    LogDeliveryConfigDTO copyConfigDTO(LogDeliveryConfigDTO deliveryConfig, LogDeliveryRecordDTO taskRecord);

    @Mapping(target = "logLimit", source = "paramDTO.limit")
    LogDeliveryDelayedConfigDTO getDelayedConfigDTO(LogDeliveryParamDTO paramDTO, List<LogDeliveryDetailDTO> deliveryDetails);

    @Mapping(target = "timeUpperLimit", ignore = true)
    @Mapping(target = "deliveryRecord", source = "deliveryRecordForTask")
    @Mapping(target = "batchLimit", ignore = true)
    LogDeliveryConfigDTO getConfigDTOForDelayedTask(LogDeliveryDelayedConfigDTO configDTO, LogDeliveryRecordDTO deliveryRecordForTask);

    @Mapping(target = "privateKey", source = "deliveryDomain.sk")
    @Mapping(target = "logLimit", source = "deliveryDomain.limit")
    @Mapping(target = "apiPath", source = "deliveryDomain.path")
    @Mapping(target = "apiBaseUrl", source = "deliveryDomain.endpoint")
    @Mapping(target = "accessKey", source = "deliveryDomain.ak")
    DeliveryLogRecoveryConfigDTO getDeliveryLogRecoveryConfigDTO(LogDeliveryDomainDTO deliveryDomain, String bizType, String targetType, Integer batchLimit);
}
