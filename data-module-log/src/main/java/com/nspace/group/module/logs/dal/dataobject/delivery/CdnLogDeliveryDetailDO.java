package com.nspace.group.module.logs.dal.dataobject.delivery;


import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :CdnLogDeliveryDetailDO.java, v0.1 2025年04月22日 10:58 zhangxin Exp
 */
@TableName(value = "cdn_log_delivery_detail")
@KeySequence("cdn_log_delivery_detail_id_seq")  // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
public class CdnLogDeliveryDetailDO {

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * 日志ID
     */
    private Long logId;

    /**
     * 域名
     */
    private String domain;

    /**
     * 日志打印时间
     */
    private LocalDateTime logTime;

    /**
     * 日志投递状态 1:成功 2:失败 4：延迟补传
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getLogTime() {
        return logTime;
    }

    public void setLogTime(LocalDateTime logTime) {
        this.logTime = logTime;
    }
}
