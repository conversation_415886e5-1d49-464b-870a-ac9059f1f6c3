package com.nspace.group.module.logs.biz.delivery;

import com.nspace.group.module.infra.dal.dataobject.cdn.OdsGeneralCdnRequestLogDO;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CdnDeliveryLogTransformServiceImpl implements CdnDeliveryLogTransformService {

    @Resource
    private JdCdnDeliveryLogTransformService jdCdnDeliveryLogTransformService;

    @Resource
    private AliCdnDeliveryLogTransformService aliCdnDeliveryLogTransformService;

    @Override
    public List<Map<String, Object>> transformLogs(String targetType, List<OdsGeneralCdnRequestLogDO> rawLogList) {
        if (LogDeliveryTargetEnum.CDN_JD.isSelf(targetType)) {
            return jdCdnDeliveryLogTransformService.transformLogs(rawLogList);
        } else if (LogDeliveryTargetEnum.CDN_ALI.isSelf(targetType)) {
            return aliCdnDeliveryLogTransformService.transformLogs(rawLogList);
        }
        throw new RuntimeException("unsupported target_type=" + targetType);
    }
}
