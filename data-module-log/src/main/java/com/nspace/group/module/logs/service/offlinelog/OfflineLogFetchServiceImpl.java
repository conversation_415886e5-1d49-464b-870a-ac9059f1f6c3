package com.nspace.group.module.logs.service.offlinelog;

import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;
import com.nspace.group.module.infra.service.detail.OdsBillingLogStreamPullService;
import com.nspace.group.module.infra.service.detail.OdsBillingLogStreamPushService;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import com.nspace.group.module.logs.convert.offlinelog.OfflineLogConvert;
import com.nspace.group.module.logs.service.offlinelog.dto.LiveStreamPullDetailDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.LiveStreamPushDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version :OfflineLogFetchServiceImpl.java, v0.1 2024年12月19日 11:52 Exp
 */
@Service
@Slf4j
public class OfflineLogFetchServiceImpl implements OfflineLogFetchService {

    @Resource
    OdsBillingLogStreamPushService pushDetailService;

    @Resource
    OdsBillingLogStreamPullService pullDetailService;

    @Override
    public List<LiveStreamPushDetailDTO> fetchPushOfflineLog(StreamDetailReqDTO reqDTO) {
        List<OdsBillingLogStreamPushDO> pushDetailList = pushDetailService.getPushDetailList(reqDTO);
        return OfflineLogConvert.INSTANCE.getStreamPushLogDTOList(pushDetailList);
    }

    @Override
    public List<LiveStreamPullDetailDTO> fetchPullOfflineLog(StreamDetailReqDTO reqDTO) {
        List<OdsBillingLogStreamPullDO> pullDetailList = pullDetailService.getPullDetailList(reqDTO);
        return OfflineLogConvert.INSTANCE.getStreamPullLogDTOList(pullDetailList);
    }
}
