package com.nspace.group.module.logs.biz.delivery;

import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogRecoveryConfigDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class LiveDeliveryLogSendServiceImpl implements LiveDeliveryLogSendService {

    @Resource
    private JdLiveDeliveryLogSendService jdLiveDeliveryLogSendService;

    @Override
    public void send(LogDeliveryConfigDTO deliveryConfig, List<Map<String, Object>> logData) {
        String targetType = deliveryConfig.getTargetType();
        if (LogDeliveryTargetEnum.LIVE_JD.isSelf(targetType)) {
            jdLiveDeliveryLogSendService.send(deliveryConfig, logData);
            return;
        }
        throw new RuntimeException("unknown_log_delivery_target,target=" + targetType);
    }

    @Override
    public boolean send(DeliveryLogRecoveryConfigDTO deliveryConfig, List<DeliveryLogDetailDTO> logDetails) {
        String targetType = deliveryConfig.getTargetType();

        if (LogDeliveryTargetEnum.LIVE_JD.isSelf(targetType)) {
            return jdLiveDeliveryLogSendService.send(deliveryConfig, logDetails);
        }
        throw new RuntimeException("unknown_log_delivery_target,target=" + targetType);
    }
}
