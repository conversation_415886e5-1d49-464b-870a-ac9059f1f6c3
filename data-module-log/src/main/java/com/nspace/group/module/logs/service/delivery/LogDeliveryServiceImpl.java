package com.nspace.group.module.logs.service.delivery;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.aliyun.openservices.aliyun.log.producer.Result;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.util.concurrent.ListenableFuture;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.module.logs.biz.delivery.CdnDeliveryLogSendService;
import com.nspace.group.module.logs.biz.delivery.LiveDeliveryLogSendService;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryConvert;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryRecordConvert;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import com.nspace.group.module.logs.service.delivery.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR>
 * @version :LogDeliveryServiceImpl.java, v0.1 2025年03月18日 03:57 Exp
 */
@Service
@Slf4j
public class LogDeliveryServiceImpl implements LogDeliveryService {

    // 投递任务Redis key过期时间（分钟）
    private final int TASK_KEY_EXPIRATION = 18;

    @Resource(name = "deliveryThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Resource
    private LiveDeliveryLogSendService liveDeliveryLogSendService;

    @Resource
    private CdnDeliveryLogSendService cdnDeliveryLogSendService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private LogDeliveryRecordService logDeliveryRecordService;

    @Resource
    private LogDeliveryDetailService logDeliveryDetailService;

    @Resource
    private LogDeliveryLogFetchService deliveryLogFetchService;


    @Override
    public void deliverLog(LogDeliveryParamDTO paramDTO) {
        String msg = validateDeliveryParam(paramDTO);
        if (!msg.equals(StringPool.EMPTY)) {
            log.error("deliverLog,param_validation_error,target={},logLimit={},err_msg={},no_op",
                    paramDTO.getTargetType(), paramDTO.getLimit(), msg);
            return;
        }

        //生成新的运行记录
        LogDeliveryRecordDTO currentRecord = logDeliveryRecordService.getCurrentDeliveryRecord(paramDTO);

        try {
            LogDeliveryConfigDTO configDTO = LogDeliveryConvert.INSTANCE.getConfigDTO(paramDTO, currentRecord);
            if (LogDeliveryTargetEnum.CDN_ALI.isSelf(paramDTO.getTargetType())) {
                log.info("deliverLog,begin_runLogDeliveryTasksAsync,target={},logLimit={},delay={},{}",
                        paramDTO.getTargetType(), paramDTO.getLimit(), paramDTO.getDelayOffset(), currentRecord);
                runLogDeliveryTasksAsync(configDTO);
                log.info("deliverLog,end_runLogDeliveryTasksAsync,target={},logLimit={},delay={},{}",
                        paramDTO.getTargetType(), paramDTO.getLimit(), paramDTO.getDelayOffset(), currentRecord);
            } else {
                log.info("deliverLog,begin_runLogDeliveryTasks,target={},logLimit={},delay={},{}",
                        paramDTO.getTargetType(), paramDTO.getLimit(), paramDTO.getDelayOffset(), currentRecord);
                runLogDeliveryTasks(configDTO);
                log.info("deliverLog,end_runLogDeliveryTasks,target={},logLimit={},delay={},{}",
                        paramDTO.getTargetType(), paramDTO.getLimit(), paramDTO.getDelayOffset(), currentRecord);
            }
        } catch (Exception e) {
            log.error("deliverLog,unknown_error,target={},logLimit={},delay={},{},errMsg={}",
                    paramDTO.getTargetType(), paramDTO.getLimit(), paramDTO.getDelayOffset(), currentRecord, e.getLocalizedMessage());
        }

    }

    @Override
    public void deliverDelayedLog(LogDeliveryParamDTO paramDTO) {
        String msg = validateDeliveryParam(paramDTO);
        if (!msg.equals(StringPool.EMPTY)) {
            log.error("deliverDelayedLog,param_validation_error,target={},logLimit={},err_msg={},no_op",
                    paramDTO.getTargetType(), paramDTO.getLimit(), msg);
            return;
        }

        try {
            if (LogDeliveryTargetEnum.CDN_ALI.isSelf(paramDTO.getTargetType())) {
                log.info("deliverDelayedLog,begin_runDelayedLogDeliveryTasksAsync,{}", paramDTO);
                runDelayedLogDeliveryTasksAsync(paramDTO);
                log.info("deliverDelayedLog,end_runDelayedLogDeliveryTasksAsync,{}", paramDTO);
            } else {
                log.info("deliverDelayedLog,begin_runDelayedLogDeliveryTasks,{}", paramDTO);
                runDelayedLogDeliveryTasks(paramDTO);
                log.info("deliverDelayedLog,end_runDelayedLogDeliveryTasks,{}", paramDTO);
            }
        } catch (Exception e) {
            log.error("deliverDelayedLog,unknown_error,{},errMsg={}", paramDTO, e.getLocalizedMessage());
        }
    }

    /**
     * 延迟日志补传<br/>
     *
     * @param paramDTO 日志投递参数
     */
    private void runDelayedLogDeliveryTasks(LogDeliveryParamDTO paramDTO) {

        String bizType = paramDTO.getBizType();

        Integer batchLimit = paramDTO.getBatchLimit();

        String targetType = paramDTO.getTargetType();

        String taskKey = calculateDelayedDeliveryTaskKey(paramDTO);
        Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(taskKey, StringPool.ONE, Duration.ofMinutes(TASK_KEY_EXPIRATION));
        //获取锁失败直接返回
        if (Boolean.FALSE.equals(success)) return;
        Long curMaxId = 0L;
        int totalLogCount = 0;
        AtomicInteger totalTaskCount = new AtomicInteger(0);
        LocalDateTime timeLowerLimit = LocalDateTime.now(ZoneOffset.ofHours(8)).minusMinutes(90L).truncatedTo(ChronoUnit.MICROS);
        ExecutorCompletionService<LogDeliveryDelayedTaskReturnDTO> completionService = new ExecutorCompletionService<>(executorService);
        try {
            while (totalLogCount <= batchLimit) {
                //批量获取当前域名需要补传的日志ID
                List<LogDeliveryDetailDTO> delayedLogDetails = logDeliveryDetailService.getDetails(bizType, paramDTO.getDomain(), batchLimit, timeLowerLimit, curMaxId);
                totalLogCount = delayedLogDetails.size();
                if (totalLogCount == 0) {
                    log.info("runDelayedLogDeliveryTasks,LogDeliveryDetailService.getDetails,no_delayed_logs,target={},batchLimit={},{}", targetType, batchLimit, paramDTO);
                    break;
                }
                log.info("runDelayedLogDeliveryTasks,fetchDelayedLogs,target={},batchLimit={},{}", targetType, batchLimit, paramDTO);
                LogDeliveryDelayedConfigDTO configDTO = LogDeliveryConvert.INSTANCE.getDelayedConfigDTO(paramDTO, delayedLogDetails);
                List<LogDeliveryDelayedTaskDTO> delayedTasks = deliveryLogFetchService.fetchDelayedLogs(configDTO);
                delayedTasks.forEach(deliveryTask -> {
                    LogDeliveryRecordDTO deliveryRecordForTask = deliveryTask.getDeliveryRecord();
                    try {
                        completionService.submit(() -> {
                            log.info("runDelayedLogDeliveryTasks,sendLogData,target={},logLimit={},{}", targetType, paramDTO.getLimit(), deliveryRecordForTask);
                            LogDeliveryConfigDTO configDTOForTask = LogDeliveryConvert.INSTANCE.getConfigDTOForDelayedTask(configDTO, deliveryRecordForTask);
                            sendLogData(configDTOForTask, deliveryTask.getLogList(), Collections.emptyMap(), null);
                            return new LogDeliveryDelayedTaskReturnDTO(bizType, deliveryRecordForTask.getDeliveryStatus(), deliveryTask.getDeliveryDetailIds());
                        });
                        totalTaskCount.getAndIncrement();
                    } catch (RejectedExecutionException e) {
                        log.error("runDelayedLogDeliveryTasks,task_rejected,{},err_msg={}", deliveryRecordForTask, e.getLocalizedMessage());
                    }
                });
                if (totalLogCount < batchLimit) {
                    break;
                }
                curMaxId = delayedLogDetails.get(delayedLogDetails.size() - 1).getId();
            }
            // 阻塞当前线程直至所有投递任务执行结束或异常退出
            while (totalTaskCount.getAndDecrement() > 0) {
                try {
                    LogDeliveryDelayedTaskReturnDTO taskReturnDTO = completionService.take().get();
                    //修改日志投递明细状态
                    logDeliveryDetailService.updateMany(taskReturnDTO.getBizType(), taskReturnDTO.getDeliveryDetailIds(), taskReturnDTO.getDeliveryStatus());
                } catch (InterruptedException | ExecutionException ignored) {
                }
            }
        } finally {
            //任务执行结束，删除redis中的key
            log.info("runDelayedLogDeliveryTasks,remove_log_delivery_task_from_registry,task={}", taskKey);
            stringRedisTemplate.delete(taskKey);
        }
    }

    /**
     * 延迟日志补传（阿里）<br/>
     *
     * @param paramDTO 日志投递参数
     */
    private void runDelayedLogDeliveryTasksAsync(LogDeliveryParamDTO paramDTO) {

        String bizType = paramDTO.getBizType();

        Integer batchLimit = paramDTO.getBatchLimit();

        String targetType = paramDTO.getTargetType();

        String taskKey = calculateDelayedDeliveryTaskKey(paramDTO);
        Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(taskKey, StringPool.ONE, Duration.ofMinutes(TASK_KEY_EXPIRATION));
        //获取锁失败直接返回
        if (Boolean.FALSE.equals(success)) return;
        Long curMaxId = 0L;
        int totalLogCount = 0;
        AtomicInteger totalTaskCount = new AtomicInteger(0);
        LocalDateTime timeLowerLimit = LocalDateTime.now(ZoneOffset.ofHours(8)).minusMinutes(90L).truncatedTo(ChronoUnit.MICROS);
        ExecutorCompletionService<LogDeliveryDelayedTaskReturnDTO> completionService = new ExecutorCompletionService<>(executorService);
        try {
            while (totalLogCount <= batchLimit) {
                //批量获取当前域名需要补传的日志ID
                List<LogDeliveryDetailDTO> delayedLogDetails = logDeliveryDetailService.getDetails(bizType, paramDTO.getDomain(), batchLimit, timeLowerLimit, curMaxId);
                totalLogCount = delayedLogDetails.size();
                if (totalLogCount == 0) {
                    log.info("runDelayedLogDeliveryTasksAsync,LogDeliveryDetailService.getDetails,no_delayed_logs,target={},batchLimit={},{}", targetType, batchLimit, paramDTO);
                    break;
                }
                log.info("runDelayedLogDeliveryTasksAsync,fetchDelayedLogs,target={},batchLimit={},{}", targetType, batchLimit, paramDTO);
                LogDeliveryDelayedConfigDTO configDTO = LogDeliveryConvert.INSTANCE.getDelayedConfigDTO(paramDTO, delayedLogDetails);
                List<LogDeliveryDelayedTaskDTO> delayedTasks = deliveryLogFetchService.fetchDelayedLogs(configDTO);
                delayedTasks.forEach(deliveryTask -> {
                    LogDeliveryRecordDTO deliveryRecordForTask = deliveryTask.getDeliveryRecord();
                    try {
                        completionService.submit(() -> {
                            log.info("runDelayedLogDeliveryTasksAsync,sendLogData,target={},logLimit={},{}", targetType, paramDTO.getLimit(), deliveryRecordForTask);
                            LogDeliveryConfigDTO configDTOForTask = LogDeliveryConvert.INSTANCE.getConfigDTOForDelayedTask(configDTO, deliveryRecordForTask);
                            Object sendRes = sendLogData(configDTOForTask, deliveryTask.getLogList(), Collections.emptyMap(), null);
                            return new LogDeliveryDelayedTaskReturnDTO(bizType, deliveryRecordForTask.getDeliveryStatus(), deliveryTask.getDeliveryDetailIds(), sendRes);
                        });
                        totalTaskCount.getAndIncrement();
                    } catch (RejectedExecutionException e) {
                        log.error("runDelayedLogDeliveryTasksAsync,task_rejected,{},err_msg={}", deliveryRecordForTask, e.getLocalizedMessage());
                    }
                });
                if (totalLogCount < batchLimit) {
                    break;
                }
                curMaxId = delayedLogDetails.get(delayedLogDetails.size() - 1).getId();
            }

            // 阻塞当前线程直至所有投递任务执行结束或异常退出
            while (totalTaskCount.getAndDecrement() > 0) {
                try {
                    LogDeliveryDelayedTaskReturnDTO taskReturnDTO = completionService.take().get();
                    Object sendRes = taskReturnDTO.getSendRes();
                    if (sendRes instanceof ListenableFuture) {
                        Object producerRes = ((ListenableFuture) sendRes).get();
                        if (producerRes instanceof Result && ((Result) producerRes).isSuccessful()) {
                            //修改日志投递明细状态
                            logDeliveryDetailService.updateMany(taskReturnDTO.getBizType(),
                                    taskReturnDTO.getDeliveryDetailIds(),
                                    LogDeliveryStatusEnum.SUCCESS.getStatus());
                        }
                    }
                } catch (Exception ignored) {
                }
            }
        } finally {
            //任务执行结束，删除redis中的key
            log.info("runDelayedLogDeliveryTasksAsync,remove_log_delivery_task_from_registry,task={}", taskKey);
            stringRedisTemplate.delete(taskKey);
        }
    }


    /**
     * 并发执行日志投递任务<br/>
     * 1、根据配置查询日志数据<br/>
     * log_time>logEndTime<br/>
     * 2、查询会根据logTime递增排序<br/>
     * 3、查询会根据batchLimit传参设置limit<br/>
     * 4、执行结束后如果查询的日志量==batchLimit说明数据没查完，循环查询并投递
     * 5、查到数据后会根据配置的logLimit拆分子任务并提交到线程池中执行
     *
     * @param deliveryConfig 日志配置参数DTO
     */
    private void runLogDeliveryTasks(LogDeliveryConfigDTO deliveryConfig) {
        Integer logLimit = deliveryConfig.getLogLimit();
        Integer batchLimit = deliveryConfig.getBatchLimit();
        String targetType = deliveryConfig.getTargetType();

        int totalLogCount = 0;
        AtomicInteger totalTaskCount = new AtomicInteger(0);
        ExecutorCompletionService<LogDeliveryTaskReturnDTO> completionService = new ExecutorCompletionService<>(executorService);
        while (totalLogCount <= batchLimit) {
            LogDeliveryRecordDTO deliveryRecord = deliveryConfig.getDeliveryRecord();
            log.info("runLogDeliveryTasks,fetchLogBatch,target={},batchLimit={},{}", targetType, batchLimit, deliveryRecord);
            LogDeliveryTasksRunDTO deliveryTasksRun = deliveryLogFetchService.fetchLogBatch(deliveryConfig);
            totalLogCount = deliveryTasksRun.getTotalLogCount();
            List<LogDeliveryTaskDTO> deliveryTasks = deliveryTasksRun.getDeliveryTasks();
            if (deliveryTasks.isEmpty()) {
                log.info("runLogDeliveryTasks,fetchLogBatch,no_task_to_run,target={},batchLimit={},{}", targetType, batchLimit, deliveryRecord);
                break;
            }
            deliveryTasks.forEach(deliveryTask -> {
                LogDeliveryRecordDTO deliveryRecordForTask = deliveryTask.getDeliveryRecord();
                String taskKey = calculateDeliveryTaskKey(deliveryRecordForTask, targetType);
                Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(taskKey, StringPool.ONE, Duration.ofMinutes(TASK_KEY_EXPIRATION));
                if (Boolean.TRUE.equals(success)) {
                    try {
                        completionService.submit(() -> {
                            log.info("runLogDeliveryTasks,sendLogData,target={},logLimit={},{}", targetType, logLimit, deliveryRecordForTask);
                            LogDeliveryConfigDTO configDTOForTask = LogDeliveryConvert.INSTANCE.copyConfigDTO(deliveryConfig, deliveryRecordForTask);
                            sendLogData(configDTOForTask, deliveryTask.getLogList(), deliveryTask.getLogIdTimeMap(), taskKey);
                            return new LogDeliveryTaskReturnDTO(deliveryRecordForTask.getBizType(), deliveryRecordForTask.getDomain(),
                                    deliveryRecordForTask.getDeliveryStatus(), deliveryTask.getLogIdTimeMap(), taskKey, null);
                        });
                        totalTaskCount.getAndIncrement();
                    } catch (RejectedExecutionException e) {
                        log.error("runLogDeliveryTasks,task_rejected,{},err_msg={}", deliveryRecordForTask, e.getLocalizedMessage());
                    }
                }
            });
            if (totalLogCount < batchLimit) {
                break;
            }
            deliveryRecord.setLogEndTime(deliveryTasksRun.getLogMaxTime());
            LogDeliveryRecordDTO nextRecord = LogDeliveryRecordConvert.INSTANCE.getNextRecordDTO(deliveryRecord);
            deliveryConfig.setDeliveryRecord(nextRecord);
        }
        //阻塞当前线程直至所有投递任务执行结束或异常退出
        while (totalTaskCount.getAndDecrement() > 0) {
            LogDeliveryTaskReturnDTO taskReturnDTO = null;
            try {
                taskReturnDTO = completionService.take().get();
                //保存日志投递明细
                logDeliveryDetailService.saveMany(taskReturnDTO.getBizType(), taskReturnDTO.getDomain(),
                        taskReturnDTO.getDeliveryStatus(), taskReturnDTO.getLogIdTimeMap());
            } catch (InterruptedException | ExecutionException ignored) {
            } finally {
                if (taskReturnDTO != null) {
                    //任务执行结束，删除redis中的key
                    String taskKey = taskReturnDTO.getTaskKey();
                    log.info("runLogDeliveryTasks,remove_log_delivery_task_from_registry,task={}", taskKey);
                    stringRedisTemplate.delete(taskKey);
                }
            }
        }
    }

    /**
     * 并发执行日志投递任务（阿里）<br/>
     *
     * @param deliveryConfig 日志配置参数DTO
     */
    private void runLogDeliveryTasksAsync(LogDeliveryConfigDTO deliveryConfig) {
        Integer logLimit = deliveryConfig.getLogLimit();
        Integer batchLimit = deliveryConfig.getBatchLimit();
        String targetType = deliveryConfig.getTargetType();

        int totalLogCount = 0;
        AtomicInteger totalTaskCount = new AtomicInteger(0);
        ExecutorCompletionService<LogDeliveryTaskReturnDTO> completionService = new ExecutorCompletionService<>(executorService);
        while (totalLogCount <= batchLimit) {
            LogDeliveryRecordDTO deliveryRecord = deliveryConfig.getDeliveryRecord();
            log.info("runLogDeliveryTasksAsync,fetchLogBatch,target={},batchLimit={},{}", targetType, batchLimit, deliveryRecord);
            LogDeliveryTasksRunDTO deliveryTasksRun = deliveryLogFetchService.fetchLogBatch(deliveryConfig);
            totalLogCount = deliveryTasksRun.getTotalLogCount();
            List<LogDeliveryTaskDTO> deliveryTasks = deliveryTasksRun.getDeliveryTasks();
            if (deliveryTasks.isEmpty()) {
                log.info("runLogDeliveryTasksAsync,fetchLogBatch,no_task_to_run,target={},batchLimit={},{}", targetType, batchLimit, deliveryRecord);
                break;
            }
            deliveryTasks.forEach(deliveryTask -> {
                LogDeliveryRecordDTO deliveryRecordForTask = deliveryTask.getDeliveryRecord();
                String taskKey = calculateDeliveryTaskKey(deliveryRecordForTask, targetType);
                Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(taskKey, StringPool.ONE, Duration.ofMinutes(TASK_KEY_EXPIRATION));
                if (Boolean.TRUE.equals(success)) {
                    try {
                        completionService.submit(() -> {
                            log.info("runLogDeliveryTasksAsync,sendLogData,target={},logLimit={},{}", targetType, logLimit, deliveryRecordForTask);
                            LogDeliveryConfigDTO configDTOForTask = LogDeliveryConvert.INSTANCE.copyConfigDTO(deliveryConfig, deliveryRecordForTask);
                            Object sendRes = sendLogData(configDTOForTask, deliveryTask.getLogList(), deliveryTask.getLogIdTimeMap(), taskKey);
                            return new LogDeliveryTaskReturnDTO(deliveryRecordForTask.getBizType(), deliveryRecordForTask.getDomain(),
                                    deliveryRecordForTask.getDeliveryStatus(), deliveryTask.getLogIdTimeMap(), taskKey, sendRes);
                        });
                        totalTaskCount.getAndIncrement();
                    } catch (RejectedExecutionException e) {
                        log.error("runLogDeliveryTasksAsync,task_rejected,{},err_msg={}", deliveryRecordForTask, e.getLocalizedMessage());
                    }
                }
            });
            if (totalLogCount < batchLimit) {
                break;
            }
            deliveryRecord.setLogEndTime(deliveryTasksRun.getLogMaxTime());
            LogDeliveryRecordDTO nextRecord = LogDeliveryRecordConvert.INSTANCE.getNextRecordDTO(deliveryRecord);
            deliveryConfig.setDeliveryRecord(nextRecord);
        }
    }

    private Object sendLogData(LogDeliveryConfigDTO deliveryConfig, List<Map<String, Object>> logData, Map<Long, LocalDateTime> logIdTimeMap, String taskKey) {
        String bizType = deliveryConfig.getDeliveryRecord().getBizType();

        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            liveDeliveryLogSendService.send(deliveryConfig, logData);
            return true;
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            return cdnDeliveryLogSendService.send(deliveryConfig, logData, logIdTimeMap, taskKey);
        } else {
            log.error("unknown_biz_type,biz_type={},no_op", bizType);
            return null;
        }
    }

    private String validateDeliveryParam(LogDeliveryParamDTO paramDTO) {
        String msg = StringPool.EMPTY;
        if (null == paramDTO) {
            msg = "delivery_param_null";
        }
        if (null == paramDTO.getTenantId()) {
            msg = "delivery_tenant_id_null";
        }
        if (StrUtil.isBlank(paramDTO.getDomain())) {
            msg = "delivery_domain_empty";
        }
        if (!BusinessTypeEnum.isBusinessType(paramDTO.getBizType())) {
            msg = "delivery_biz_type_unknown";
        }
        if (StrUtil.isBlank(paramDTO.getType())) {
            msg = "delivery_log_type_empty";
        }
        if (!LogDeliveryTargetEnum.isLogDeliveryTarget(paramDTO.getTargetType())) {
            msg = "delivery_target_unknown";
        }
        return msg;
    }

    private String calculateDeliveryTaskKey(LogDeliveryRecordDTO deliveryRecord, String targetType) {
        String keyBody = String.join(StringPool.EMPTY, targetType, deliveryRecord.getTenantId().toString(),
                deliveryRecord.getDomain(), deliveryRecord.getBizType(), deliveryRecord.getLogType(),
                deliveryRecord.getLogStartId().toString(), deliveryRecord.getLogEndId().toString(),
                deliveryRecord.getLogStartTime().toString(), deliveryRecord.getLogEndTime().toString());
        return String.join(StringPool.COLON, "log_delivery_task", SecureUtil.md5(keyBody));
    }

    private String calculateDelayedDeliveryTaskKey(LogDeliveryParamDTO paramDTO) {
        String keyBody = String.join(StringPool.EMPTY, paramDTO.getTargetType(), paramDTO.getTenantId().toString(),
                paramDTO.getDomain(), paramDTO.getBizType(), paramDTO.getType());
        return String.join(StringPool.COLON, "delayed_log_delivery_task", SecureUtil.md5(keyBody));
    }

}
