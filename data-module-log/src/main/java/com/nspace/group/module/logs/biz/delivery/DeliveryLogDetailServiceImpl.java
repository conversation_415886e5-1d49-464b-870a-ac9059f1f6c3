package com.nspace.group.module.logs.biz.delivery;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.module.logs.dal.handler.StreamLoadHandler;
import com.nspace.group.module.logs.service.delivery.DeliveryCdnLogDetailService;
import com.nspace.group.module.logs.service.delivery.DeliveryLiveLogDetailService;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 第三方日志投递明细接口实现类
 *
 * <AUTHOR>
 * @since 2025-05-12 14:32:16
 */
@Service
@Slf4j
public class DeliveryLogDetailServiceImpl implements DeliveryLogDetailService {

    private final JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
            .serializationInclusion(JsonInclude.Include.NON_NULL)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    @Resource(name = "nspaceLogStreamLoader")
    private StreamLoadHandler streamLoadHandler;

    @Resource
    DeliveryLiveLogDetailService liveDetailService;

    @Resource
    DeliveryCdnLogDetailService cdnLogDetailService;

    @Override
    public List<DeliveryLogDetailDTO> getDetails(String bizType, String targetType, LocalDateTime timeLowerLimit, Integer limit) {
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            return liveDetailService.getDetails(targetType, timeLowerLimit, limit);
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            return cdnLogDetailService.getDetails(targetType, timeLowerLimit, limit);
        }
        throw new RuntimeException("unsupported biz_type=" + bizType);
    }

    @Override
    public void updateMany(String bizType, Collection<DeliveryLogDetailDTO> logDetails) {
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            streamLoadData("delivery_live_log_fail_detail", logDetails);
            return;
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            streamLoadData("delivery_cdn_log_fail_detail", logDetails);
            return;
        }
        throw new RuntimeException("unsupported biz_type=" + bizType);
    }

    private void streamLoadData(String tableName, Collection<DeliveryLogDetailDTO> logDetails) {
        try {
            Map<String, String> loadResultMap = streamLoadHandler.sendData(tableName, jsonMapper.writeValueAsString(logDetails));
            if ("Success".equals(loadResultMap.get("Status"))) {
                log.info("stream_load_success,NumberLoadedRows={},LoadTimeMs={}", loadResultMap.get("NumberLoadedRows"), loadResultMap.get("LoadTimeMs"));
            } else {
                log.error("stream_load_failed,Message={},ErrorURL={}", loadResultMap.get("Message"), loadResultMap.get("ErrorURL"));
                throw new RuntimeException("stream_load_failed");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
