package com.nspace.group.module.logs.service.offlinelog;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SequenceWriter;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Service("defaultOfflineLogTransform")
@Slf4j
public class DefaultOfflineLogTransformServiceImpl implements OfflineLogTransformService {

    private static final JsonMapper JSON_MAPPER = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    @Override
    public String serialize(List<Map<String, String>> logDataList) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(outputStream);
             SequenceWriter sequenceWriter = JSON_MAPPER.writer().withRootValueSeparator(StringPool.NEWLINE).writeValues(bufferedOutputStream)) {
            sequenceWriter.writeAll(logDataList);
            return outputStream.toString(StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("serialize,unknown_io_error,err_msg={}", e.getLocalizedMessage());
            throw new RuntimeException(e);
        }
    }

    @Override
    public byte[] compress(List<Map<String, String>> logDataList) {
        if (CollectionUtil.isEmpty(logDataList)) return new byte[]{};
        String jsonString = serialize(logDataList);
        return compress(jsonString);
    }


    @Override
    public byte[] compress(String str) {
        if (StrUtil.isBlank(str)) return new byte[]{};
        return ZipUtil.gzip(str.getBytes(StandardCharsets.UTF_8));
    }
}
