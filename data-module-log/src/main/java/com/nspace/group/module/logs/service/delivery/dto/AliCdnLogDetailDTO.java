package com.nspace.group.module.logs.service.delivery.dto;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 数据明细-阿里通用CDN计费数据明细表DTO
 *
 * <AUTHOR>
 * @since 2025-04-22 16:16:56
 */
@Data
@NoArgsConstructor
public class AliCdnLogDetailDTO {

    //节点标识 (固定值)
    private String station;

    //Unix时间戳，单位秒
    private Long unixtime;

    //请求方法
    private String method;

    //请求协议
    private String scheme;

    //域名
    private String domain;

    //URI
    private String uri;

    //请求参数 (包含问号)
    private String uriParam;

    //返回码
    private Integer returnCode;

    //返回时间 (毫秒)
    private Long requestTime;

    //请求大小 (字节)
    private Long requestSize;

    //返回大小 (字节)
    private Long responseSize;

    //命中信息
    private String hitInfo;

    //客户端IP
    private String connectIp;

    //节点IP
    private String serverAddr;

    //节点端口
    private Integer serverPort;

    //Referer
    private String referer;

    //内容类型
    private String contentType;

    //User-Agent
    private String userAgent;

    //客户端口
    private Integer remotePort;

    //文件最后修改时间
    private String lastModified;

    //请求唯一ID
    private String uuid;

    //Via头信息
    private String viaInfo;

    //应答首字节时间 (毫秒)
    private Long responseFbtTime;

    //实际发送body大小
    private Long bodyBytesSent;

    //Content-Length响应头
    private Long sentHttpContentLength;

    //请求Range头
    private String httpRange;

    //应答头里表示的range信息(由源站创建)
    private String sentHttpContentRange;

    //业务定制字段
    private String dyUserInfo;

    //X-Forwarded-For
    private String httpXForwardedFor;

    //CDN节点所在的国家2字母缩写(ISO 3166)
    private String country;

    //HTTP协议版本
    private String httpVersion;

    //QUIC协议标记
    private String quic;
}

