package com.nspace.group.module.logs.convert.delivery;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.module.infra.client.ApiException;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;
import com.nspace.group.module.logs.client.model.JdLiveLogDeliveryResult;
import com.nspace.group.module.logs.service.delivery.dto.JdLiveLogDetailDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :LiveCdnLogConvert.java, v0.1 2025年03月19日 10:21 zhangxin Exp
 */
@Mapper
public interface LiveCdnLogConvert {

    ZoneOffset stdZoneOffset = ZoneOffset.ofHours(8);

    DateTimeFormatter logTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssxxx");

    LiveCdnLogConvert INSTANCE = Mappers.getMapper(LiveCdnLogConvert.class);
    JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();


    @Mapping(target = "userPin", constant = "Y2FpZGFhbg==")
    @Mapping(target = "timeDelay", constant = "0L")
    @Mapping(target = "serverName", constant = "-")
    @Mapping(target = "sentHttpContentRange", constant = "-")
    @Mapping(target = "requestLength", constant = "0.0")
    @Mapping(target = "rawStreamname", qualifiedByName = "rawStreamnameConvert", source = "requestUri")
    @Mapping(target = "publishDelay", constant = "0L")
    @Mapping(target = "protocol", source = "scheme")
    @Mapping(target = "internalStatus", constant = "-")
    @Mapping(target = "httpRange", constant = "-")
    @Mapping(target = "gopDurSbuf", constant = "0L")
    @Mapping(target = "gopCntSbuf", constant = "0L")
    @Mapping(target = "firstPktSentTime", constant = "0L")
    @Mapping(target = "cookie", constant = "-")
    @Mapping(target = "contentType", constant = "-")
    @Mapping(target = "cacheStatus", constant = "-")
    @Mapping(target = "audioGap", constant = "0L")
    @Mapping(target = "audioBytes", constant = "0L")
    @Mapping(target = "adjacentGap", constant = "0L")
    @Mapping(target = "XCdnSrcProto", constant = "-")
    @Mapping(target = "videoBytes", source = "intervalBytesRecv")
    @Mapping(target = "videoGap", source = "videoMaxGapMs")
    @Mapping(target = "videoDroppedRatio", constant = "0.0")
    @Mapping(target = "uri", qualifiedByName = "uriConvert", source = "requestUri")
    @Mapping(target = "upstreamResponseTime", constant = "0.0")
    @Mapping(target = "upstreamResponseCode", constant = "200")
    @Mapping(target = "upstreamHeaderTime", constant = "0.0")
    @Mapping(target = "upstreamFirstByteTime", constant = "0.0")
    @Mapping(target = "upstreamConnectTime", constant = "0.0")
    @Mapping(target = "upstreamBytesReceived", constant = "0.0")
    @Mapping(target = "traceId", source = "requestId")
    @Mapping(target = "totalSentBytes", source = "bytesSent")
    @Mapping(target = "bytesSent", source = "intervalBytesSent")
    @Mapping(target = "totalBodySentBytes", constant = "0L")
    @Mapping(target = "timeIso8601", expression = "java(rawLog.getLogTime().toInstant().atOffset(stdZoneOffset).format(logTimeFormatter))")
    @Mapping(target = "streamStatus", expression = "java(rawLog.getRound() == 0 ? -1 : rawLog.getEnd())")
    @Mapping(target = "sessionId", ignore = true)
    @Mapping(target = "requestTime", expression = "java(new java.math.BigDecimal(rawLog.getTotalDuration()).setScale(3, java.math.RoundingMode.HALF_DOWN).divide(new java.math.BigDecimal(\"1000\"), java.math.RoundingMode.HALF_DOWN))")
    @Mapping(target = "requestMethod", constant = "publish")
    @Mapping(target = "remoteAddr", qualifiedByName ="remoteServerAddrConvert", source = "clientAddr")
    @Mapping(target = "serverAddr", qualifiedByName ="remoteServerAddrConvert", source = "serverAddr")
    @Mapping(target = "msec", qualifiedByName ="msecConvert", source = "logTime")
    @Mapping(target = "httpUserAgent", source = "userAgent")
    @Mapping(target = "httpReferer", source = "referer")
    @Mapping(target = "host", source = "domain")
    @Mapping(target = "framesSent", constant = "0L")
    @Mapping(target = "framesReceived", expression = "java(rawLog.getVideoFps() * rawLog.getDuration())")
    @Mapping(target = "firstGopSentTime", source = "firstGopSentTimeMs")
    @Mapping(target = "errorDiscription", source = "err")
    @Mapping(target = "chargeBy", source = "domain")
    @Mapping(target = "cacheLevel", constant = "L1")
    @Mapping(target = "bytesReceived", source = "bytesRecv")
    @Mapping(target = "bodyBytesSent", constant = "0L")
    @Mapping(target = "avgGopSize", source = "avgGopSizeMs")
    @Mapping(target = "audiosFpsWarn", constant = "0")
    @Mapping(target = "audioFramesSent", constant = "0L")
    @Mapping(target = "audioFramesReceived", constant = "0L")
    @Mapping(target = "audioDroppedRatio", constant = "0.0")
    @Mapping(target = "connectTime", expression = "java(rawLog.getConnectTime().toInstant().toEpochMilli())")
    @Mapping(target = "sourceStreamFps", constant = "0.0")
    @Mapping(target = "upstreamNode", constant = "-")
    @Mapping(target = "category", constant = "YUNZHAN")
    @Mapping(target = "discontinuousCount", expression = "java(rawLog.getDiscontinuousCount() == null ? 0L : rawLog.getDiscontinuousCount())")
    @Mapping(target = "discontinuousTime", expression = "java(rawLog.getDiscontinuousTime() == null ? 0L : rawLog.getDiscontinuousTime())")
    @Mapping(target = "status", qualifiedByName ="statusConvert", source = "status")
    @Mapping(target = "via", constant = "-")
    @Mapping(target = "scheme", qualifiedByName = "schemeConvert", source = "streamProtocol")
    JdLiveLogDetailDTO getJdLivePushLogDetailDTO(OdsBillingLogStreamPushDO rawLog);

    List<JdLiveLogDetailDTO> getJdLivePushLogDetailDTOList(List<OdsBillingLogStreamPushDO> rawLogList);

    @Mapping(target = "userPin", constant = "Y2FpZGFhbg==")
    @Mapping(target = "timeDelay", constant = "0L")
    @Mapping(target = "serverName", constant = "-")
    @Mapping(target = "sentHttpContentRange", constant = "-")
    @Mapping(target = "requestLength", constant = "0.0")
    @Mapping(target = "rawStreamname", qualifiedByName = "rawStreamnameConvert", source = "requestUri")
    @Mapping(target = "publishDelay", constant = "0L")
    @Mapping(target = "protocol", source = "scheme")
    @Mapping(target = "internalStatus", constant = "-")
    @Mapping(target = "httpRange", constant = "-")
    @Mapping(target = "gopDurSbuf", constant = "0L")
    @Mapping(target = "gopCntSbuf", constant = "0L")
    @Mapping(target = "firstPktSentTime", constant = "0L")
    @Mapping(target = "cookie", constant = "-")
    @Mapping(target = "contentType", constant = "-")
    @Mapping(target = "cacheStatus", constant = "-")
    @Mapping(target = "audioGap", constant = "0L")
    @Mapping(target = "audioBytes", constant = "0L")
    @Mapping(target = "adjacentGap", constant = "0L")
    @Mapping(target = "XCdnSrcProto", constant = "-")
    @Mapping(target = "videoBytes", source = "intervalBytesRecv")
    @Mapping(target = "videoGap", source = "videoMaxGapMs")
    @Mapping(target = "videoDroppedRatio", constant = "0.0")
    @Mapping(target = "uri", qualifiedByName = "uriConvert", source = "requestUri")
    @Mapping(target = "upstreamResponseTime", constant = "0.0")
    @Mapping(target = "upstreamResponseCode", constant = "200")
    @Mapping(target = "upstreamHeaderTime", constant = "0.0")
    @Mapping(target = "upstreamFirstByteTime", constant = "0.0")
    @Mapping(target = "upstreamConnectTime", constant = "0.0")
    @Mapping(target = "upstreamBytesReceived", constant = "0.0")
    @Mapping(target = "traceId", source = "requestId")
    @Mapping(target = "totalSentBytes", source = "bytesSent")
    @Mapping(target = "bytesSent", source = "intervalBytesSent")
    @Mapping(target = "totalBodySentBytes", constant = "0L")
    @Mapping(target = "timeIso8601", expression = "java(rawLog.getLogTime().toInstant().atOffset(stdZoneOffset).format(logTimeFormatter))")
    @Mapping(target = "streamStatus", expression = "java(rawLog.getRound() == 0 ? -1 : rawLog.getEnd())")
    @Mapping(target = "sessionId", ignore = true)
    @Mapping(target = "requestTime", expression = "java(new java.math.BigDecimal(rawLog.getTotalDuration()).setScale(3, java.math.RoundingMode.HALF_DOWN).divide(new java.math.BigDecimal(\"1000\"), java.math.RoundingMode.HALF_DOWN))")
    @Mapping(target = "requestMethod", constant = "play")
    @Mapping(target = "remoteAddr", qualifiedByName ="remoteServerAddrConvert", source = "clientAddr")
    @Mapping(target = "serverAddr", qualifiedByName ="remoteServerAddrConvert", source = "serverAddr")
    @Mapping(target = "msec", qualifiedByName ="msecConvert", source = "logTime")
    @Mapping(target = "httpUserAgent", source = "userAgent")
    @Mapping(target = "httpReferer", source = "referer")
    @Mapping(target = "host", source = "domain")
    @Mapping(target = "framesSent", constant = "0L")
    @Mapping(target = "framesReceived", expression = "java(rawLog.getVideoFps() * rawLog.getDuration())")
    @Mapping(target = "firstGopSentTime", source = "firstGopSentTimeMs")
    @Mapping(target = "errorDiscription", source = "err")
    @Mapping(target = "chargeBy", source = "domain")
    @Mapping(target = "cacheLevel", constant = "L1")
    @Mapping(target = "bytesReceived", source = "bytesRecv")
    @Mapping(target = "bodyBytesSent", constant = "0L")
    @Mapping(target = "avgGopSize", source = "avgGopSizeMs")
    @Mapping(target = "audiosFpsWarn", constant = "0")
    @Mapping(target = "audioFramesSent", constant = "0L")
    @Mapping(target = "audioFramesReceived", constant = "0L")
    @Mapping(target = "audioDroppedRatio", constant = "0.0")
    @Mapping(target = "connectTime", expression = "java(rawLog.getConnectTime().toInstant().toEpochMilli())")
    @Mapping(target = "sourceStreamFps", constant = "0.0")
    @Mapping(target = "upstreamNode", constant = "-")
    @Mapping(target = "category", constant = "YUNZHAN")
    @Mapping(target = "discontinuousCount", expression = "java(rawLog.getDiscontinuousCount() == null ? 0L : rawLog.getDiscontinuousCount())")
    @Mapping(target = "discontinuousTime", expression = "java(rawLog.getDiscontinuousTime() == null ? 0L : rawLog.getDiscontinuousTime())")
    @Mapping(target = "status", qualifiedByName ="statusConvert", source = "status")
    @Mapping(target = "via", constant = "-")
    @Mapping(target = "scheme", qualifiedByName = "schemeConvert", source = "streamProtocol")
    JdLiveLogDetailDTO getJdLivePullLogDetailDTO(OdsBillingLogStreamPullDO rawLog);

    List<JdLiveLogDetailDTO> getJdLivePullLogDetailDTOList(List<OdsBillingLogStreamPullDO> rawLogList);

    @Named("msecConvert")
    default BigDecimal getMsec(Date logTime) {
        long logTimeMillis = logTime.toInstant().toEpochMilli();
        return new BigDecimal(logTimeMillis).setScale(3, RoundingMode.HALF_DOWN).divide(new BigDecimal("1000"), java.math.RoundingMode.HALF_DOWN);
    }

    @Named("uriConvert")
    default String getUri(String requestUri) {
        if (StrUtil.isBlank(requestUri)) {
            return "-";
        } else {
            int queryBegin = requestUri.indexOf('?');
            return queryBegin != -1 ? requestUri.substring(0, queryBegin) : requestUri;
        }
    }

    @Named("remoteServerAddrConvert")
    default String getRemoteServerAddr(String addr) {
        if (StrUtil.isBlank(addr)) {
            return "-";
        } else {
            int portBegin = addr.indexOf(':', addr.indexOf('.'));
            return portBegin != -1 ? addr.substring(0, portBegin) : addr;
        }
    }

    @Named("statusConvert")
    default String getStatus(Integer status) {
        return status == null || status.equals(0) ? "200" : String.valueOf(status);
    }

    @Named("schemeConvert")
    default String getScheme(String streamProtocol) {
        if (StrUtil.isBlank(streamProtocol)) {
            return "-";
        } else {
            return "http_flv".equalsIgnoreCase(streamProtocol) ? "hdl"
                    : "http_hls".equalsIgnoreCase(streamProtocol) ? "hls"
                    : streamProtocol;
        }
    }

    @Named("rawStreamnameConvert")
    default String getRawStreamname(String requestUri) {
        if (StrUtil.isBlank(requestUri)) {
            return "-";
        } else {
            int queryBegin = requestUri.indexOf('?');
            queryBegin = queryBegin != -1 ? queryBegin : requestUri.length();
            int extBegin = requestUri.indexOf('.');
            extBegin = extBegin != -1 ? extBegin : requestUri.length();
            int appStreamEnd = Math.min(extBegin, queryBegin);
            String[] splits = requestUri.substring(0, appStreamEnd).split("/");
            return splits[splits.length - 1];
        }
    }
    @Named("chargeByConvert")
    default String getChargeBy(String requestUri, String domain) {
        if (StrUtil.isBlank(requestUri) || StrUtil.isBlank(domain)) {
            return domain;
        }
        int queryIdx = requestUri.indexOf('?');
        String path = queryIdx != -1 ? requestUri.substring(0, queryIdx) : requestUri;
        String[] splits = path.split("/");
        if (splits.length < 3) {
            return domain;
        }
        String streamName = splits[splits.length - 1];
        String appName = splits[splits.length - 2];
        return String.format("%s==%s==%s", domain, appName, streamName);
    }

    @AfterMapping
    default void getJdLivePushLogSessionId(@MappingTarget JdLiveLogDetailDTO logDetail, OdsBillingLogStreamPushDO rawLog) {
        Integer dataPlatform = rawLog.getDataPlatform();
        String scheme = logDetail.getScheme();
        logDetail.setSessionId(String.join(StringPool.UNDERSCORE, scheme,
                dataPlatform.toString(), DigestUtil.md5Hex(rawLog.getRequestId())));
    }

    @AfterMapping
    default void getJdLivePullLogSessionId(@MappingTarget JdLiveLogDetailDTO logDetail, OdsBillingLogStreamPullDO rawLog) {
        Integer dataPlatform = rawLog.getDataPlatform();
        String scheme = logDetail.getScheme();
        logDetail.setSessionId(String.join(StringPool.UNDERSCORE, scheme,
                dataPlatform.toString(), DigestUtil.md5Hex(rawLog.getRequestId())));
    }

    @AfterMapping
    default void getJdLivePullLogChargeBy(@MappingTarget JdLiveLogDetailDTO logDetail, OdsBillingLogStreamPullDO rawLog) {
        logDetail.setChargeBy(getChargeBy(rawLog.getRequestUri(), rawLog.getDomain()));
    }

    default List<Map<String, Object>> getLiveJDPushLogMapList(List<OdsBillingLogStreamPushDO> rawLogList) {
        if (rawLogList.isEmpty()) return Collections.emptyList();
        List<JdLiveLogDetailDTO> logDTOList = getJdLivePushLogDetailDTOList(rawLogList);
        TypeFactory typeFactory = jsonMapper.getTypeFactory();
        TypeReference<HashMap<String, Object>> typeRef = new TypeReference<>() {};
        JavaType javaType = typeFactory.constructType(typeRef);
        return jsonMapper.convertValue(logDTOList, typeFactory.constructCollectionType(List.class, javaType));
    }

    default List<Map<String, Object>> getLiveJDPullLogMapList(List<OdsBillingLogStreamPullDO> rawLogList) {
        if (rawLogList.isEmpty()) return Collections.emptyList();
        List<JdLiveLogDetailDTO> logDTOList = getJdLivePullLogDetailDTOList(rawLogList);
        TypeFactory typeFactory = jsonMapper.getTypeFactory();
        TypeReference<HashMap<String, Object>> typeRef = new TypeReference<>() {};
        JavaType javaType = typeFactory.constructType(typeRef);
        return jsonMapper.convertValue(logDTOList, typeFactory.constructCollectionType(List.class, javaType));
    }

    @Mapping(target = "respMsg", source = "message")
    @Mapping(target = "respHeaders", source = "responseHeaders")
    @Mapping(target = "respCode", source = "code")
    @Mapping(target = "respBody", source = "responseBody")
    @Mapping(target = "msg", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "requestId", ignore = true)
    JdLiveLogDeliveryResult getErrorLogDeliveryResult(ApiException e);

    @Named("strToMapListConvert")
    default List<Map<String, Object>> getLogMapList(String logJsonArr) throws JsonProcessingException {
        TypeFactory typeFactory = jsonMapper.getTypeFactory();
        TypeReference<HashMap<String, Object>> typeRef = new TypeReference<>() {};
        JavaType javaType = typeFactory.constructType(typeRef);
        List<Map<String, Object>> logMapList = jsonMapper.readValue(logJsonArr, typeFactory.constructCollectionType(List.class, javaType));
        return logMapList.stream().map(logMap -> {
            Map<String, Object> newLogMap = new HashMap<>(logMap.size());
            logMap.forEach((k, v) -> newLogMap.put(toSnakeCase(k), v));
            return newLogMap;
        }).collect(Collectors.toList());
    }

    @Named("snakeCaseConvert")
    default String toSnakeCase(String camelCaseKey) {
        if (StrUtil.isBlank(camelCaseKey)) return camelCaseKey;
        StringBuilder builder = new StringBuilder(camelCaseKey.length());
        char c = camelCaseKey.charAt(0);
        builder.append(Character.toLowerCase(c));
        for (int i = 1; i < camelCaseKey.length(); i++) {
            char ch = camelCaseKey.charAt(i);
            if (Character.isUpperCase(ch)) {
                builder.append(StringPool.UNDERSCORE).append(Character.toLowerCase(ch));
            } else {
                builder.append(ch);
            }
        }
        return builder.toString();
    }
}
