package com.nspace.group.module.logs.service.delivery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.logs.convert.delivery.LiveLogDeliveryDetailConvert;
import com.nspace.group.module.logs.dal.dataobject.delivery.LiveLogDeliveryDetailDO;
import com.nspace.group.module.logs.dal.mapper.delivery.LiveLogDeliveryDetailMapper;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryDetailDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 日志投递详情服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-04-10 14:32:16
 */
@Service
@DS("nspace_log")
@Validated
public class LiveLogDeliveryDetailServiceImpl implements LiveLogDeliveryDetailService {

    @Resource
    LiveLogDeliveryDetailMapper deliveryDetailMapper;

    @Override
    public List<LogDeliveryDetailDTO> getDetails(String domain, Integer batchLimit, LocalDateTime timeLowerLimit, Long curMaxId) {
        LambdaQueryWrapperX<LiveLogDeliveryDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.select(LiveLogDeliveryDetailDO::getId, LiveLogDeliveryDetailDO::getLogId, LiveLogDeliveryDetailDO::getStatus, LiveLogDeliveryDetailDO::getCreateTime)
                .eq(LiveLogDeliveryDetailDO::getDomain, domain).ge(LiveLogDeliveryDetailDO::getCreateTime, timeLowerLimit)
                .ne(LiveLogDeliveryDetailDO::getStatus, LogDeliveryStatusEnum.SUCCESS.getStatus())
                .gt(LiveLogDeliveryDetailDO::getId, curMaxId)
                .orderByAsc(LiveLogDeliveryDetailDO::getId);

        Page<LiveLogDeliveryDetailDO> page = new Page<>(1, batchLimit);
        List<LiveLogDeliveryDetailDO> delayedLogRecords = deliveryDetailMapper.selectPage(page.setSearchCount(false), queryWrapper).getRecords();
        return LiveLogDeliveryDetailConvert.INSTANCE.getDeliveryDetailDTOList(delayedLogRecords);
    }

    @Override
    public void updateMany(Set<Long> deliveryDetailIds, Integer deliveryStatus) {
        if (LogDeliveryStatusEnum.SUCCESS.isSelf(deliveryStatus)) {
            if (!deliveryDetailIds.isEmpty()) {
                LambdaUpdateWrapper<LiveLogDeliveryDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(LiveLogDeliveryDetailDO::getStatus, LogDeliveryStatusEnum.SUCCESS.getStatus())
                        .in(LiveLogDeliveryDetailDO::getId, deliveryDetailIds);
                deliveryDetailMapper.update(updateWrapper);
            }
        }

    }

    @Override
    public void saveMany(String domain, Integer deliveryStatus, Map<Long, LocalDateTime> logIdTimeMap) {
        List<LogDeliveryDetailDTO> deliveryDetails = logIdTimeMap.entrySet().stream()
                .map(entry -> LiveLogDeliveryDetailConvert.INSTANCE.newDetailDTO(domain, entry.getKey(), entry.getValue(), deliveryStatus))
                .collect(Collectors.toList());
        List<LiveLogDeliveryDetailDO> detailDOList = LiveLogDeliveryDetailConvert.INSTANCE.getDetailDOList(deliveryDetails);
        Map<Boolean, List<LiveLogDeliveryDetailDO>> partitions = detailDOList.stream().collect(Collectors.partitioningBy(detailDO -> detailDO.getId() == null));
        List<LiveLogDeliveryDetailDO> updateDetailDOS = partitions.get(Boolean.FALSE);
        deliveryDetailMapper.insertBatch(partitions.get(Boolean.TRUE));
        if (!updateDetailDOS.isEmpty()) {
            deliveryDetailMapper.updateBatch(updateDetailDOS, updateDetailDOS.size());
        }
    }
}

