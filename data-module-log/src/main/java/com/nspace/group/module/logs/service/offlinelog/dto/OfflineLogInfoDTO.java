package com.nspace.group.module.logs.service.offlinelog.dto;

import java.time.LocalDateTime;

/**
 * OfflineLogInfoDTO
 *
 * <AUTHOR>
 */
public class OfflineLogInfoDTO {

    /**
     * 编号
     */
    private Long id;

    /**
     * 多租户编号
     */
    private Long tenantId;

    /**
     * 域名
     */
    private String domain;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * ID最大值
     */
    private Long maxId;

    /**
     * 日志类型 1、PUSH, 2、PULL
     */
    private String type;

    /**
     * 下载链接
     */
    private String url;

    /**
     * 日志文件名称
     */
    private String fileName;

    /**
     * 日志文件大小
     */
    private Integer fileSize;

    /**
     * 文件状态
     */
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Long getMaxId() {
        return maxId;
    }

    public void setMaxId(Long maxId) {
        this.maxId = maxId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getFileSize() {
        return fileSize;
    }

    public void setFileSize(Integer fileSize) {
        this.fileSize = fileSize;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
