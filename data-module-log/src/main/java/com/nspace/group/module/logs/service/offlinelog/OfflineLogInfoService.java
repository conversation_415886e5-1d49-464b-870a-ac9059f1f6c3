package com.nspace.group.module.logs.service.offlinelog;


import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogFileSaveDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogInfoDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogLiveDomainDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version :OfflineLogService.java, v0.1 2024年12月17日 11:52 Exp
 */
public interface OfflineLogInfoService {


    /**
     * 获取租户域名对应的上次日志结束时间
     *
     * @param tenantDomain 租户ID&Domain
     * @param interval 日志生成间隔
     * @return OfflineLogInfoDTO
     */
    OfflineLogInfoDTO getCurrentLogInfoForDomain(OfflineLogLiveDomainDTO tenantDomain, Integer interval);

    /**
     * 获取租户域名对应的处理失败记录
     *
     * @param tenantDomain 租户ID&Domain
     * @return List<OfflineLogInfoDTO>
     */
    List<OfflineLogInfoDTO> getFailedLogInfosForDomains(OfflineLogLiveDomainDTO tenantDomain);

    /**
     * 根据文件信息保存离线日志记录
     *
     * @param saveDTO     日志文件上传元数据
     * @param endpointUrl OSS服务器url
     * @return Long
     */
    Long save(OfflineLogFileSaveDTO saveDTO, String endpointUrl);

    /**
     * 根据数据明细查询条件保存离线日志记录
     *
     * @param saveDTO 数据明细查询条件
     * @param status 文件生成状态
     * @return Long
     */
    Long save(StreamDetailReqDTO saveDTO, Integer status);

}
