package com.nspace.group.module.logs.service.delivery.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 数据明细-拉流计费数据明细表DTO
 *
 * <AUTHOR>
 * @since 2024-12-18 16:41:56
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class JdLiveLogDetailDTO {

    private String timeIso8601;
    private BigDecimal msec;
    private String requestId;
    private String sessionId;
    private String remoteAddr;
    private String serverAddr;
    private String requestMethod;
    private String scheme;
    private String host;
    private String requestUri;
    private String uri;
    private String protocol;
    private String status;
    private String cacheStatus;
    private String httpReferer;
    private String sentHttpContentRange;
    private String contentType;
    private String serverName;
    private BigDecimal requestTime;
    private Long bytesReceived;
    private Long bytesSent;
    private Long bodyBytesSent;
    private BigDecimal requestLength;
    private BigDecimal upstreamBytesReceived;
    private BigDecimal upstreamConnectTime;
    private BigDecimal upstreamHeaderTime;
    private BigDecimal upstreamResponseTime;
    private BigDecimal upstreamFirstByteTime;
    private Integer upstreamResponseCode;
    private String via;
    private String cacheLevel;
    private String chargeBy;
    private String category;
    private String hostname;
    private String httpUserAgent;
    private String httpRange;
    private Long framesReceived;
    private Long framesSent;
    private Long connectTime;
    private Long discontinuousCount;
    private Long discontinuousTime;
    private String traceId;
    private BigDecimal sourceStreamFps;
    private Long audioFramesReceived;
    private Long audioFramesSent;
    private Integer audiosFpsWarn;
    private Long firstGopSentTime;
    private BigDecimal avgGopSize;
    private Long videoBytes;
    private Long audioBytes;
    private Long videoGap;
    private Long audioGap;
    private Long adjacentGap;
    private BigDecimal videoDroppedRatio;
    private BigDecimal audioDroppedRatio;
    private Long gopCntSbuf;
    private Long gopDurSbuf;
    private Long timeDelay;
    private Long firstPktSentTime;
    private String errorDiscription;
    private Long totalSentBytes;
    private Long totalBodySentBytes;
    private Integer streamStatus;
    private String serverProtocol;
    private String httpMethod;
    private String cookie;
    @JsonProperty("x-cdn-src-proto")
    private String xCdnSrcProto;
    private String rawStreamname;
    private String internalStatus;
    private Long publishDelay;
    private String upstreamNode;
    private String userPin;
}

