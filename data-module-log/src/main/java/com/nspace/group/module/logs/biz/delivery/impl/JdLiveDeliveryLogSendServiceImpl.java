package com.nspace.group.module.logs.biz.delivery.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.ApiException;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.nspace.group.module.logs.biz.delivery.DeliveryLogDetailService;
import com.nspace.group.module.logs.biz.delivery.JdLiveDeliveryLogSendService;
import com.nspace.group.module.logs.client.api.JdLogDeliveryApi;
import com.nspace.group.module.logs.client.impl.auth.JdApiAuth;
import com.nspace.group.module.logs.client.impl.factory.JdApiClientFactory;
import com.nspace.group.module.logs.client.model.JdLiveLogDeliveryResult;
import com.nspace.group.module.logs.convert.delivery.LiveCdnLogConvert;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.service.delivery.LogDeliveryLogDataService;
import com.nspace.group.module.logs.service.delivery.LogDeliveryRecordService;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogRecoveryConfigDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryConfigDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :JdLiveDeliveryLogSendServiceImpl.java, v0.1 2025年03月31日 15:36 Exp
 */
@Service
@Slf4j
public class JdLiveDeliveryLogSendServiceImpl implements JdLiveDeliveryLogSendService {

    @Resource
    private LogDeliveryRecordService logDeliveryRecordService;

    @Resource
    private LogDeliveryLogDataService logDeliveryLogDataService;

    @Resource
    private DeliveryLogDetailService deliveryLogDetailService;

    @Override
    public void send(LogDeliveryConfigDTO deliveryConfig, List<Map<String, Object>> logData) {
        String targetType = deliveryConfig.getTargetType();
        String apiBaseUrl = deliveryConfig.getApiBaseUrl();
        String apiPath = deliveryConfig.getApiPath();
        LogDeliveryRecordDTO deliveryRecord = deliveryConfig.getDeliveryRecord();

        int curFailCount = -1;
        int retryCount = deliveryRecord.getRetryCount() == null ? 0 : deliveryRecord.getRetryCount();

        try {
            byte[] compressedLogData = logDeliveryLogDataService.compress(logData);
            deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.INIT.getStatus());
            logDeliveryRecordService.save(deliveryRecord);
            JdLiveLogDeliveryResult deliveryResult;
            //获取ApiClient
            Authentication authentication = new JdApiAuth(apiPath, deliveryConfig.getPrivateKey(), deliveryConfig.getAccessKey());
            ApiClient apiClient = JdApiClientFactory.INSTANCE.createApiClient(apiBaseUrl, authentication);
            //创建JdLogDeliveryApi对象
            JdLogDeliveryApi logDeliveryApi = new JdLogDeliveryApi(apiClient);

            /*
            第一次失败：立即重试
            第二次失败：等待 2 秒
            第三次失败：等待 4 秒
            第四次失败：等待 8 秒
            */
            while (curFailCount <= 3) {
                if (curFailCount > 0) {
                    int waitTimeMillis = (2 << curFailCount) / 2 * 1000;
                    Thread.sleep(waitTimeMillis);
                }
                deliveryResult = sendDeliveryApiCall(logDeliveryApi, compressedLogData);
                String resultJson = JsonUtils.toJsonString(deliveryResult);
                if (deliveryResult.isDeliverySuccessful()) {
                    deliveryRecord.setRetryCount(retryCount);
                    deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.SUCCESS.getStatus());
                    deliveryRecord.setDeliveryResult(resultJson);
                    logDeliveryRecordService.save(deliveryRecord);
                    log.info("sendLogData,log_delivered,target={},{},apiBaseUrl={},apiPath={}",
                            targetType, deliveryRecord, apiBaseUrl, apiPath);
                    break;
                }
                deliveryRecord.setRetryCount(retryCount);
                deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.RETRY.getStatus());
                deliveryRecord.setDeliveryResult(resultJson);
                log.info("sendLogData,retrying[{}],target={},{},apiBaseUrl={},apiPath={},result={}",
                        retryCount, targetType, deliveryRecord, apiBaseUrl, apiPath, resultJson);
                retryCount++;
                curFailCount++;
            }
            if (!LogDeliveryStatusEnum.SUCCESS.isSelf(deliveryRecord.getDeliveryStatus())) {
                //依然失败则持续重试15分钟每次间隔10秒
                TimeInterval timer = DateUtil.timer();
                int retryLimitMillis = 900_000;
                while (timer.interval() <= retryLimitMillis) {
                    Thread.sleep(10_000);
                    deliveryResult = sendDeliveryApiCall(logDeliveryApi, compressedLogData);
                    String resultJson = JsonUtils.toJsonString(deliveryResult);
                    deliveryRecord.setDeliveryResult(resultJson);
                    if (deliveryResult.isDeliverySuccessful()) {
                        deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.SUCCESS.getStatus());
                        logDeliveryRecordService.save(deliveryRecord);
                        log.info("sendLogData,log_delivered,target={},{},apiBaseUrl={},apiPath={}",
                                targetType, deliveryRecord, apiBaseUrl, apiPath);
                        break;
                    }
                    deliveryRecord.setRetryCount(retryCount);
                    if (timer.interval() > retryLimitMillis) {
                        deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.STALE.getStatus());
                        logDeliveryRecordService.save(deliveryRecord);
                        log.info("sendLogData,log_delivery_failed,target={},{},apiBaseUrl={},apiPath={},retry_count={}",
                                targetType, deliveryRecord, apiBaseUrl, apiPath, retryCount);
                        break;
                    }
                    log.info("sendLogData,retrying[{}],target={},{},apiBaseUrl={},apiPath={},result={}",
                            retryCount, targetType, deliveryRecord, apiBaseUrl, apiPath, resultJson);
                    retryCount++;
                }
            }
        } catch (Exception e) {
            log.error("JdLiveDeliveryLogSendServiceImpl.send,unknown_exception,target={},{},apiBaseUrl={},apiPath={},error={}",
                    targetType, deliveryRecord, apiBaseUrl, apiPath, e.getLocalizedMessage());
            deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.STALE.getStatus());
            deliveryRecord.setDeliveryResult(e.getCause() != null ? e.getCause().getLocalizedMessage() : e.getLocalizedMessage());
            logDeliveryRecordService.save(deliveryRecord);
        }

    }

    @Override
    public boolean send(DeliveryLogRecoveryConfigDTO deliveryConfig, List<DeliveryLogDetailDTO> logDetails) {
        String targetType = deliveryConfig.getTargetType();
        String apiBaseUrl = deliveryConfig.getApiBaseUrl();
        String apiPath = deliveryConfig.getApiPath();

        boolean result = false;

        try {
            String jsonArrStr = logDetails.stream()
                    .map(DeliveryLogDetailDTO::getLogJson)
                    .collect(Collectors.joining(StringPool.COMMA, StringPool.LEFT_SQ_BRACKET, StringPool.RIGHT_SQ_BRACKET));
            List<Map<String, Object>> logMapList = LiveCdnLogConvert.INSTANCE.getLogMapList(jsonArrStr);
            byte[] compressedData = logDeliveryLogDataService.compress(logMapList);
            //获取ApiClient
            Authentication authentication = new JdApiAuth(apiPath, deliveryConfig.getPrivateKey(), deliveryConfig.getAccessKey());
            ApiClient apiClient = JdApiClientFactory.INSTANCE.createApiClient(apiBaseUrl, authentication);
            //创建JdLogDeliveryApi对象
            JdLogDeliveryApi logDeliveryApi = new JdLogDeliveryApi(apiClient);
            JdLiveLogDeliveryResult deliveryResult = sendDeliveryApiCall(logDeliveryApi, compressedData);
            log.info("send,jd_live_log_recovery,after_api_call,apiBaseUrl={},apiPath={},result={}",
                    apiBaseUrl, apiPath, JsonUtils.toJsonString(deliveryResult));
            if (deliveryResult.isDeliverySuccessful()) {
                logDetails.forEach(deliveryRecord -> {
                    deliveryRecord.setLogStatus(LogDeliveryStatusEnum.SUCCESS.getStatus());
                    deliveryRecord.setDeliveryTimestamp(LocalDateTime.now());
                });
                log.info("send,jd_live_log_recovery,log_delivered,apiBaseUrl={},apiPath={}", apiBaseUrl, apiPath);
                result = true;
            } else {
                log.info("send,jd_live_log_recovery,delivery_failure,apiBaseUrl={},apiPath={},error={}",
                        apiBaseUrl, apiPath, deliveryResult.getRespMsg());
            }
            deliveryLogDetailService.updateMany(deliveryConfig.getBizType(), logDetails);
        } catch (Exception e) {
            log.error("send,jd_live_log_recovery,unknown_exception,target={},apiBaseUrl={},apiPath={},error={}",
                    targetType, apiBaseUrl, apiPath, e.getLocalizedMessage());
        }
        return result;
    }

    private JdLiveLogDeliveryResult sendDeliveryApiCall(JdLogDeliveryApi logDeliveryApi, byte[] logData) {
        JdLiveLogDeliveryResult deliveryResult;
        try {
            deliveryResult = logDeliveryApi.postLogs(logData);
        } catch (ApiException e) {
            deliveryResult = LiveCdnLogConvert.INSTANCE.getErrorLogDeliveryResult(e);
        }
        return deliveryResult;
    }
}
