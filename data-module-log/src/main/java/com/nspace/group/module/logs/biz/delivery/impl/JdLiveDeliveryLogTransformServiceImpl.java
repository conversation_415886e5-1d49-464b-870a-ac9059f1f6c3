package com.nspace.group.module.logs.biz.delivery.impl;

import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;
import com.nspace.group.module.logs.biz.delivery.JdLiveDeliveryLogTransformService;
import com.nspace.group.module.logs.convert.delivery.LiveCdnLogConvert;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class JdLiveDeliveryLogTransformServiceImpl implements JdLiveDeliveryLogTransformService {
    @Override
    public List<Map<String, Object>> transformPushLogs(List<OdsBillingLogStreamPushDO> rawLogList) {
        return LiveCdnLogConvert.INSTANCE.getLiveJDPushLogMapList(rawLogList);
    }

    @Override
    public List<Map<String, Object>> transformPullLogs(List<OdsBillingLogStreamPullDO> rawLogList) {
        return LiveCdnLogConvert.INSTANCE.getLiveJDPullLogMapList(rawLogList);
    }
}
