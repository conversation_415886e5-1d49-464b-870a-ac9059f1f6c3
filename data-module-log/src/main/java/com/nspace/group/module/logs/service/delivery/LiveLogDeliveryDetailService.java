package com.nspace.group.module.logs.service.delivery;

import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryDetailDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 直播日志投递详情服务接口
 *
 * <AUTHOR>
 * @since 2025-04-10 14:32:16
 */
public interface LiveLogDeliveryDetailService {

    /**
     * 获取日志投递明细记录
     *
     * @param domain         域名
     * @param batchLimit     数据量限制
     * @param timeLowerLimit 创建时间下限
     * @param curMaxId       批次id下限
     * @return 日志投递明细列表
     */
    List<LogDeliveryDetailDTO> getDetails(String domain, Integer batchLimit, LocalDateTime timeLowerLimit, Long curMaxId);

    /**
     * 日志投递成功，更新投递明细状态
     *
     * @param deliveryDetailIds 待更新的明细ID
     * @param deliveryStatus    日志投递状态
     */
    void updateMany(Set<Long> deliveryDetailIds, Integer deliveryStatus);

    /**
     * 新增日志投递明细列表
     *
     * @param domain         域名
     * @param deliveryStatus 投递状态
     * @param logIdTimeMap   日志ID和日志时间
     */
    void saveMany(String domain, Integer deliveryStatus, Map<Long, LocalDateTime> logIdTimeMap);
}

