package com.nspace.group.module.logs.service.offlinelog;


import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogLiveDomainDTO;

/**
 * <AUTHOR>
 * @version :OfflineLogGenerateService.java, v0.1 2024年12月18日 11:52 Exp
 */
public interface OfflineLogGenerateService {

    /**
     * 生成离线日志数据
     *
     * @param tenantDomain 需要生成离线日志的域名
     * @param interval 时间间隔（分钟）
     */
    void generateOfflineLog(OfflineLogLiveDomainDTO tenantDomain, Integer interval);
}
