package com.nspace.group.module.logs.service.offlinelog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nspace.group.module.infra.enums.offlinelog.OfflineLogStatusEnum;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import com.nspace.group.module.logs.convert.offlinelog.OfflineLogInfoConvert;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.OfflineLogInfoMapper;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogFileSaveDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogInfoDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogLiveDomainDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :OfflineLogInfoServiceImpl.java, v0.1 2024年12月17日 11:25 Exp
 */
@Service
@DS("nspace_log")
@Validated
public class OfflineLogInfoServiceImpl implements OfflineLogInfoService {

    @Resource
    OfflineLogInfoMapper offlineLogInfoMapper;

    @Override
    public OfflineLogInfoDTO getCurrentLogInfoForDomain(OfflineLogLiveDomainDTO tenantDomain, Integer interval) {

        Long tenantId = tenantDomain.getTenantId();
        String domain = tenantDomain.getDomain();
        String type = tenantDomain.getType();
        QueryWrapper<OfflineLogInfoDO> queryWrapper = new QueryWrapper<>();
        String endTimeExp = String.format("MAX(end_time) + INTERVAL '%s' MINUTE AS end_time", interval);
        queryWrapper.select("tenant_id", "domain", "type", endTimeExp)
                .eq("tenant_id", tenantId).eq("domain", domain).eq("type", type)
                .groupBy("tenant_id", "domain", "type");

        Map<List<String>, Map<String, OfflineLogInfoDTO>> tenantDomainLogInfosMap = offlineLogInfoMapper.selectList(queryWrapper)
                .stream()
                .collect(Collectors.groupingBy(
                                logInfoDO -> Arrays.asList(logInfoDO.getTenantId().toString(), logInfoDO.getDomain()),
                                Collectors.mapping(OfflineLogInfoConvert.INSTANCE::toOfflineLogInfoDTO,
                                        Collectors.toMap(
                                                OfflineLogInfoDTO::getType,
                                                Function.identity(),
                                                (OfflineLogInfoDTO k1, OfflineLogInfoDTO k2) -> k1))
                        )
                );
        Map<String, OfflineLogInfoDTO> typeLogInfoMap = tenantDomainLogInfosMap.getOrDefault(Arrays.asList(tenantId.toString(), domain), Collections.emptyMap());
        return typeLogInfoMap.getOrDefault(type, OfflineLogInfoConvert.INSTANCE.newDTO(tenantDomain));
    }

    @Override
    public List<OfflineLogInfoDTO> getFailedLogInfosForDomains(OfflineLogLiveDomainDTO tenantDomain) {
        if (tenantDomain == null) return Collections.emptyList();

        LambdaQueryWrapper<OfflineLogInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OfflineLogInfoDO::getDeleted, 0).eq(OfflineLogInfoDO::getStatus, OfflineLogStatusEnum.FAILED.getStatus());
        queryWrapper.eq(OfflineLogInfoDO::getTenantId, tenantDomain.getTenantId()).eq(OfflineLogInfoDO::getDomain, tenantDomain.getDomain());
        return offlineLogInfoMapper.selectList(queryWrapper).stream()
                .map(OfflineLogInfoConvert.INSTANCE::toOfflineLogInfoDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Long save(OfflineLogFileSaveDTO saveDTO, String endpointUrl) {
        OfflineLogInfoDO logInfoDO = OfflineLogInfoConvert.INSTANCE.fromFileSaveDTO(saveDTO, endpointUrl);
        offlineLogInfoMapper.insertOrUpdate(logInfoDO);
        return logInfoDO.getId();
    }

    @Override
    public Long save(StreamDetailReqDTO saveDTO, Integer status) {
        OfflineLogInfoDO logInfoDO = OfflineLogInfoConvert.INSTANCE.getDOFromReqDTO(saveDTO, status);
        offlineLogInfoMapper.insertOrUpdate(logInfoDO);
        return logInfoDO.getId();
    }
}
