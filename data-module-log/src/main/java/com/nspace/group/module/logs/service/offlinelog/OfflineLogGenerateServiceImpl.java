package com.nspace.group.module.logs.service.offlinelog;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.primitives.Bytes;
import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import com.nspace.group.module.infra.enums.offlinelog.OfflineLogStatusEnum;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import com.nspace.group.module.logs.convert.offlinelog.OfflineLogConvert;
import com.nspace.group.module.logs.convert.offlinelog.OfflineLogInfoConvert;
import com.nspace.group.module.logs.service.offlinelog.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version :OfflineLogGenerateServiceImpl.java, v0.1 2024年12月18日 11:52 Exp
 */
@Service
@Slf4j
public class OfflineLogGenerateServiceImpl implements OfflineLogGenerateService {

    //日志文件后缀
    private static final String FILE_EXT = ".log.gz";
    //日志查询批次大小
    private static final int BATCH_SIZE = 5000;

    //日志文件大小上线
    private static final int FILE_TRUNK_LIMIT = 6 * 1024 * 1024;

    private static final ZoneOffset DEFAULT_OFFSET = ZoneOffset.ofHours(8);

    //日志文件名时间部分formatter
    private static final DateTimeFormatter START_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    @Resource
    OfflineLogFileService offlineLogFileService;

    @Resource
    OfflineLogInfoService offlineLogInfoService;

    @Resource
    OfflineLogFetchService offlineLogFetchService;

    @Resource(name = "defaultOfflineLogTransform")
    OfflineLogTransformService offlineLogTransformService;

    @Override
    public void generateOfflineLog(OfflineLogLiveDomainDTO tenantDomain, Integer interval) {
        if (tenantDomain == null) return;
        //生成本次日志生成记录
        OfflineLogInfoDTO nextLogInfo = offlineLogInfoService.getCurrentLogInfoForDomain(tenantDomain, interval);
        //查域名历次日志生成失败的记录
        List<OfflineLogInfoDTO> failedLogInfos = offlineLogInfoService.getFailedLogInfosForDomains(tenantDomain);

        //失败记录有ID，新纪录无ID
        List<OfflineLogInfoDTO> mergedLogInfos = new ArrayList<>(failedLogInfos);
        mergedLogInfos.add(nextLogInfo);

        mergedLogInfos.forEach(mergedLogInfo -> {
            try {
                log.info("fetchLogDataAndUploadFile,fetch_upload_file,tenant_id={},domain={},type={},end_time={},max_id={}",
                        mergedLogInfo.getTenantId(), mergedLogInfo.getDomain(), mergedLogInfo.getType(), mergedLogInfo.getEndTime(), mergedLogInfo.getMaxId());
                fetchLogDataAndUploadFile(mergedLogInfo, interval);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("fetchLogDataAndUploadFile,fetch_upload_file_error,tenant_id={},domain={},type={},end_time={},max_id={},err_msg={},continue",
                        mergedLogInfo.getTenantId(), mergedLogInfo.getDomain(), mergedLogInfo.getType(), mergedLogInfo.getEndTime(), mergedLogInfo.getMaxId(), e.getLocalizedMessage());
            }
        });
    }

    private void fetchLogDataAndUploadFile(OfflineLogInfoDTO logInfoDTO, Integer interval) {
        LocalDateTime startTime = resolveStartTime(logInfoDTO.getEndTime(), interval);
        StreamDetailReqDTO detailReqDTO = OfflineLogInfoConvert.INSTANCE.getStreamDetailReqDTO(logInfoDTO, startTime, BATCH_SIZE);

        List<String[]> tempFileInfos = incrementalFetchAndUpload(detailReqDTO);
        try {
            if (!tempFileInfos.isEmpty()) {
                String fileName = resolveFileName(logInfoDTO.getEndTime(), logInfoDTO.getDomain());
                OfflineLogFileSaveDTO fileSaveDTO = OfflineLogInfoConvert.INSTANCE.toFileSaveDTO(logInfoDTO, fileName);
                offlineLogFileService.mergeFile(fileSaveDTO, tempFileInfos);
            }
        } finally {
            tempFileInfos.forEach(tempFileInfo -> {
                log.info("fetchLogDataAndUploadFile,merge_succeeded,remove_temp_file,file={}", Arrays.toString(tempFileInfo));
                offlineLogFileService.removeFile(tempFileInfo[0], tempFileInfo[1]);
            });
        }
    }

    /**
     * 循环拉取上传给定时间区间的日志数据
     *
     * @param detailReqDTO 查询参数
     * @return [["bucket","path","fileSize"],["bucket","path","fileSize"],...]
     */
    private List<String[]> incrementalFetchAndUpload(StreamDetailReqDTO detailReqDTO) {
        List<String[]> tempFileInfoList = new ArrayList<>();
        StreamDetailReqDTO reqDTO = OfflineLogInfoConvert.INSTANCE.newStreamDetailReqDTO(detailReqDTO, detailReqDTO.getStartTime(), 0L);
        String tenantId = reqDTO.getTenantId();
        String domain = reqDTO.getDomain();
        String logType = reqDTO.getType();
        LocalDateTime endTime = reqDTO.getEndTime();
        try {
            byte[] logDataBytesSoFar = new byte[]{};
            AtomicInteger logCounter = new AtomicInteger(0);
            AtomicInteger trunkCounter = new AtomicInteger(0);
            AtomicInteger batchCounter = new AtomicInteger(0);
            while (true) {
                LocalDateTime startTime = reqDTO.getStartTime();
                long maxId = reqDTO.getMinId();
                LocalDateTime curMaxTs = null;
                Long curMaxId = null;
                List<Map<String, String>> logDataList;
                if (LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.getCode().equals(logType)) {
                    log.info("OfflineLogFetchService.fetchPushOfflineLog,fetching_log,tenant_id={},domain={},start_time={},end_time={},max_id={}",
                            tenantId, domain, startTime, endTime, maxId);
                    List<LiveStreamPushDetailDTO> pushDetailDTOS = offlineLogFetchService.fetchPushOfflineLog(reqDTO);
                    logDataList = OfflineLogConvert.INSTANCE.getPushLogMapList(pushDetailDTOS);
                    if (pushDetailDTOS.size() > 0) {
                        LiveStreamPushDetailDTO maxLogTimeEntry = pushDetailDTOS.get(pushDetailDTOS.size() - 1);
                        curMaxTs = LocalDateTime.ofInstant(maxLogTimeEntry.getLogTime().toInstant(), DEFAULT_OFFSET);
                        curMaxId = maxLogTimeEntry.getId();
                    }
                } else if (LiveDomainTypeEnum.DOMAIN_TYPE_PULL.getCode().equals(logType)) {
                    log.info("OfflineLogFetchService.fetchPullOfflineLog,fetching_log,tenant_id={},domain={},start_time={},end_time={},max_id={}",
                            tenantId, domain, startTime, endTime, maxId);
                    List<LiveStreamPullDetailDTO> pullDetailDTOS = offlineLogFetchService.fetchPullOfflineLog(reqDTO);
                    logDataList = OfflineLogConvert.INSTANCE.getPullLogMapList(pullDetailDTOS);
                    if (pullDetailDTOS.size() > 0) {
                        LiveStreamPullDetailDTO maxLogTimeEntry = pullDetailDTOS.get(pullDetailDTOS.size() - 1);
                        curMaxTs = LocalDateTime.ofInstant(maxLogTimeEntry.getLogTime().toInstant(), DEFAULT_OFFSET);
                        curMaxId = maxLogTimeEntry.getId();
                    }
                } else {
                    log.error("incrementalFetchAndUpload,unknown_log_type,type={},no_op", logType);
                    logDataList = Collections.emptyList();
                }
                logCounter.addAndGet(logDataList.size());
                log.info("incrementalFetchAndUpload,tenant_id={},domain={},start_time={},end_time={},total_count_sr_far={}",
                        tenantId, domain, startTime, endTime, logCounter.get());

                batchCounter.incrementAndGet();

                //不是第一批次且当前日志数据不为空则在生成的json字符串前拼接newline
                String json = logDataList.isEmpty() ? StringPool.EMPTY : offlineLogTransformService.serialize(logDataList);
                if (batchCounter.get() != 1 && StrUtil.isNotBlank(json)) {
                    json = StringPool.NEWLINE + json;
                }

                byte[] compressedLogDataListInBytes = offlineLogTransformService.compress(json);
                logDataBytesSoFar = Bytes.concat(logDataBytesSoFar, compressedLogDataListInBytes);
                if (logDataList.size() < BATCH_SIZE) {
                    if (trunkCounter.get() > 0) {
                        if (logDataList.size() > 0) {
                            //最终批次同样创建临时文件
                            //增加批次号
                            trunkCounter.incrementAndGet();
                            log.info("incrementalFetchAndUpload,final_trunk,upload_temp_log_file,tenant_id={},domain={},start_time={},end_time={},trunk_num={},log_count={},sample_log_line={}",
                                    tenantId, domain, startTime, endTime, trunkCounter.get(), logDataList.size(), logDataList.get(0));
                            //创建临时文件
                            String fileName = resolveTempFileName(detailReqDTO.getEndTime(), startTime, domain, logType, trunkCounter.get());
                            OfflineLogFileSaveDTO fileSaveDTO = OfflineLogInfoConvert.INSTANCE.fromReqDTO(reqDTO, fileName, logDataBytesSoFar);
                            String[] tempFileBucketPath = offlineLogFileService.createTempFile(fileSaveDTO);
                            if (tempFileBucketPath.length == 3) {
                                tempFileInfoList.add(tempFileBucketPath);
                            }
                        }
                    } else {
                        if (logCounter.get() > 0) {
                            //数据量未达到限制，直接上传完整文件
                            log.info("incrementalFetchAndUpload,final_batch,upload_log_file,tenant_id={},domain={},start_time={},end_time={},batch_num={}",
                                    tenantId, domain, startTime, endTime, batchCounter.get());
                            //上传日志文件
                            String fileName = resolveFileName(detailReqDTO.getEndTime(), domain);
                            OfflineLogFileSaveDTO fileSaveDTO = OfflineLogInfoConvert.INSTANCE.fromReqDTO(detailReqDTO, fileName, logDataBytesSoFar);
                            offlineLogFileService.createFile(fileSaveDTO);
                        } else if (logCounter.get() == 0){
                            //当前时间段无数据，仅保存记录
                            log.info("incrementalFetchAndUpload,no_log_data,save_record,tenant_id={},domain={},start_time={},end_time={},batch_num={}",
                                    tenantId, domain, startTime, endTime, batchCounter.get());
                            //生成记录，不上传文件
                            offlineLogInfoService.save(detailReqDTO, OfflineLogStatusEnum.SUCCESS.getStatus());
                        }
                    }
                    break;
                } else {
                    log.info("incrementalFetchAndUpload,batch_size_reached,tenant_id={},domain={},start_time={},end_time={},batch_num={},log_count={},sample_log_line={}",
                            tenantId, domain, startTime, endTime, batchCounter.get(), logDataList.size(), logDataList.get(0));
                    //数据量达到限制，创建临时文件
                    if (logDataBytesSoFar.length >= FILE_TRUNK_LIMIT) {
                        trunkCounter.incrementAndGet();
                        log.info("incrementalFetchAndUpload,trunk_limit_reached,upload_temp_log_file,tenant_id={},domain={},start_time={},end_time={},trunk_num={},log_count={},sample_log_line={}",
                                tenantId, domain, startTime, endTime, trunkCounter.get(), logDataList.size(), logDataList.get(0));
                        String fileName = resolveTempFileName(detailReqDTO.getEndTime(), startTime, domain, logType, trunkCounter.get());
                        OfflineLogFileSaveDTO fileSaveDTO = OfflineLogInfoConvert.INSTANCE.fromReqDTO(reqDTO, fileName, logDataBytesSoFar);
                        String[] tempFileBucketPath = offlineLogFileService.createTempFile(fileSaveDTO);
                        if (tempFileBucketPath.length == 3) {
                            tempFileInfoList.add(tempFileBucketPath);
                            logDataBytesSoFar = new byte[]{};
                        }
                    }
                    //设置下一批次开始时间和ID最大值
                    reqDTO = OfflineLogInfoConvert.INSTANCE.newStreamDetailReqDTO(reqDTO, curMaxTs, curMaxId);
                }
            }
            return tempFileInfoList;
        } catch (Exception e) {
            tempFileInfoList.forEach(tempFilePath -> {
                log.info("incrementalFetchAndUpload,unknown_error,remove_temp_file,file={}", Arrays.toString(tempFilePath));
                offlineLogFileService.removeFile(tempFilePath[0], tempFilePath[1]);
            });
            //报错，仅保存记录
            log.error("incrementalFetchAndUpload,unknown_error,save_record,tenant_id={},domain={},end_time={},err_msg={}",
                    tenantId, domain, detailReqDTO.getStartTime(), e.getLocalizedMessage());
            offlineLogInfoService.save(detailReqDTO, OfflineLogStatusEnum.FAILED.getStatus());

            throw new RuntimeException(e);
        }
    }

    /**
     * 拼接日志文件名称
     *
     * @param endTime 日志开始时间
     * @param domain  域名信息
     * @return String
     */
    private String resolveFileName(LocalDateTime endTime, String domain) {
        String endTimePart = endTime.format(START_TIME_FORMATTER);
        return endTimePart + StringPool.DASH + domain + FILE_EXT;
    }

    /**
     * 拼接临时日志文件名称
     *
     * @param origStartTime 时间段开始时间
     * @param incrStartTime 时间分段开始时间
     * @param domain        域名
     * @param logType       日志类型
     * @param batchCount    批次序号
     * @return 临时文件名
     */
    private String resolveTempFileName(LocalDateTime origStartTime, LocalDateTime incrStartTime, String domain, String logType, int batchCount) {
        String origStartTimePart = origStartTime.format(START_TIME_FORMATTER);
        String incrStartTimePart = incrStartTime.format(START_TIME_FORMATTER);
        return String.join(StringPool.DASH, origStartTimePart, domain, incrStartTimePart, logType.toLowerCase(), String.valueOf(batchCount), System.currentTimeMillis() + FILE_EXT);
    }

    private LocalDateTime resolveStartTime(LocalDateTime endTime, long interval) {
        return endTime.minus(Duration.ofMinutes(interval));
    }
}
