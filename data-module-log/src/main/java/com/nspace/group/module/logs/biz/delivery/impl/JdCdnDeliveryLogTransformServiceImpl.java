package com.nspace.group.module.logs.biz.delivery.impl;

import com.nspace.group.module.infra.dal.dataobject.cdn.OdsGeneralCdnRequestLogDO;
import com.nspace.group.module.logs.biz.delivery.JdCdnDeliveryLogTransformService;
import com.nspace.group.module.logs.convert.delivery.GeneralCdnLogConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version :JdCdnDeliveryLogTransformServiceImpl.java, v0.1 2025年03月19日 11:36 Exp
 */
@Service
@Slf4j
public class JdCdnDeliveryLogTransformServiceImpl implements JdCdnDeliveryLogTransformService {

    @Override
    public List<Map<String, Object>> transformLogs(List<OdsGeneralCdnRequestLogDO> rawLogList) {
        return GeneralCdnLogConvert.INSTANCE.getCdnJDLogMapList(rawLogList);
    }
}