package com.nspace.group.module.logs.service.delivery.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * 数据明细-通用CDN计费数据明细表DTO
 *
 * <AUTHOR>
 * @since 2025-03-19 10:16:56
 */
@Data
@NoArgsConstructor
public class JdCdnLogDetailDTO {

    @JsonIgnore
    private Long id;
    //日志打印时间, ISO 8601标准格式
    @JsonIgnore
    private Date logTime;

    private String timeIso8601;
    private Long msec;
    private String requestId;
    private String remoteAddr;
    private String serverAddr;
    private String requestMethod;
    private String scheme;
    private String host;
    private String requestUri;
    private String uri;
    private String serverProtocol;
    private Integer status;
    private String cacheStatus;
    private String httpReferer;
    private String sentHttpContentRange;
    private String contentType;
    private String serverName;
    private Double requestTime;
    private String bytesReceived = "-";
    private Long bytesSent;
    private Long bodyBytesSent;
    private Long requestLength;
    private String upstreamBytesReceived = "-";
    private String upstreamConnectTime = "-";
    private String upstreamHeaderTime = "-";
    private String upstreamResponseTime = "-";
    private String upstreamFirstByteTime = "-";
    private Integer upstreamResponseCode = 200;
    private String via;
    private String cacheLevel = "-";
    private String chargeBy = "-";
    private String category = "-";
    private String hostname = "-";
    private String httpUserAgent;
    private String httpRange;
    private String tcpinfoRtt = "-";
    private String tcpinfoRttvar = "-";
    private String tcpinfoSndCwnd = "-";
    private String tcpinfoRcvSpace = "-";
    private String sentHttpLocation = "-";
    private String sentHttpContentLength = "-";
    private String mgoPlainUrl = "-";
    private String mgoConnection = "-";
    private String httpXForwardedFor;
    private String cdnCharge = "-";
    private String pin = "-";
    private String domainType = "-";
    private String connection = "-";
    private String bytesSendUntil = "-";
    private String isFinish = "-";
    private String sentHttpContentType = "-";
    private Integer remotePort;
    private String sentHttpLastModified = "-";
    private String upstreamAddr = "-";
    private String dataCenter = "-";
    private String dnsView = "-";
    private String sslEarlyData = "-";
    private String macAddr = "-";
    private String hitBytes = "-";
    private String sentHttpContentEncoding = "-";
}

