package com.nspace.group.module.logs.service.offlinelog;

import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogFileSaveDTO;

import java.util.List;

/**
 * 文件上传记录 Service 接口
 *
 * <AUTHOR>
 */
public interface OfflineLogFileService {

    /**
     * 创建文件上传记录
     *
     * @param createReqVO 创建信息
     */
    void createFile(OfflineLogFileSaveDTO createReqVO);

    /**
     * 上传临时文件（不记录）
     *
     * @param createReqVO 创建信息
     * @return bucket和文件路径
     */
    String[] createTempFile(OfflineLogFileSaveDTO createReqVO);


    /**
     * 删除文件
     *
     * @param bucket 桶
     * @param path 路径
     */
    void removeFile(String bucket, String path);


    /**
     * 合并文件
     * @param fileSaveDTO saveDTO
     * @param fileList 源文件列表
     */
    void mergeFile(OfflineLogFileSaveDTO fileSaveDTO, List<String[]> fileList);

    /**
     * 校验桶是否存在，不存在则创建
     *
     * @param bucket 桶
     */
    void checkCreateBucket(String bucket);

}