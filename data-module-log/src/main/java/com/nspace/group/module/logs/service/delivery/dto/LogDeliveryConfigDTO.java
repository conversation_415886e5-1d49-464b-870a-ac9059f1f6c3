package com.nspace.group.module.logs.service.delivery.dto;

import cn.hutool.core.lang.Assert;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;

import java.time.LocalDateTime;

/**
 * 日志查询参数DTO
 *
 * <AUTHOR>
 */
public class LogDeliveryConfigDTO {

    // 日志投递目标
    private final String targetType;

    // 日志投递记录项
    private LogDeliveryRecordDTO deliveryRecord;

    // 单次投递数量限制
    private final Integer logLimit;

    // 批次查询限制
    private final Integer batchLimit;

    // 双方约定的唯一标识
    private final String privateKey;
    // ak
    private final String accessKey;

    // 接口path
    private final String apiPath;

    // 接口基础Url
    private final String apiBaseUrl;

    // 日志时间上限
    private final LocalDateTime timeUpperLimit;

    //------阿里CDN日志投递相关------
    // 日志项目
    private final String project;

    // 日志存储
    private final String logStore;

    public LogDeliveryConfigDTO(String targetType, LogDeliveryRecordDTO deliveryRecord,
                                Integer logLimit, Integer batchLimit, String privateKey, String accessKey,
                                String apiPath, String apiBaseUrl, LocalDateTime timeUpperLimit, String project, String logStore) {
        Assert.notBlank(targetType);
        Assert.notNull(deliveryRecord);
        Assert.notBlank(privateKey);
        Assert.notBlank(accessKey);
        if (!LogDeliveryTargetEnum.CDN_ALI.isSelf(targetType)) {
            Assert.notBlank(apiPath);
        }
        Assert.notBlank(apiBaseUrl);
        this.targetType = targetType;
        this.deliveryRecord = deliveryRecord;
        this.logLimit = logLimit;
        this.batchLimit = batchLimit;
        this.privateKey = privateKey;
        this.accessKey = accessKey;
        this.apiPath = apiPath;
        this.apiBaseUrl = apiBaseUrl;
        this.timeUpperLimit = timeUpperLimit;
        this.project = project;
        this.logStore = logStore;
    }

    public String getTargetType() {
        return targetType;
    }

    public LogDeliveryRecordDTO getDeliveryRecord() {
        return deliveryRecord;
    }

    public void setDeliveryRecord(LogDeliveryRecordDTO deliveryRecord) {
        this.deliveryRecord = deliveryRecord;
    }

    public Integer getLogLimit() {
        return logLimit;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public String getApiPath() {
        return apiPath;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    public Integer getBatchLimit() {
        return batchLimit;
    }

    public LocalDateTime getTimeUpperLimit() {
        return timeUpperLimit;
    }

    public String getProject() {
        return project;
    }

    public String getLogStore() {
        return logStore;
    }
}
