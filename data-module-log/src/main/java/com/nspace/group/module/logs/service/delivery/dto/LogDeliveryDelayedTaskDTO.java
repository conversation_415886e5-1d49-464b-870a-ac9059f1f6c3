package com.nspace.group.module.logs.service.delivery.dto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 延迟日志投递任务DTO
 *
 * <AUTHOR>
 */
public class LogDeliveryDelayedTaskDTO {

    // 日志数据
    private final List<Map<String, Object>> logList;

    private final LogDeliveryRecordDTO deliveryRecord;

    private final Set<Long> deliveryDetailIds;

    public LogDeliveryDelayedTaskDTO(List<Map<String, Object>> logList, LogDeliveryRecordDTO deliveryRecord, Set<Long> deliveryDetailIds) {
        this.logList = logList;
        this.deliveryRecord = deliveryRecord;
        this.deliveryDetailIds = deliveryDetailIds;
    }

    public List<Map<String, Object>> getLogList() {
        return logList;
    }

    public LogDeliveryRecordDTO getDeliveryRecord() {
        return deliveryRecord;
    }

    public Set<Long> getDeliveryDetailIds() {
        return deliveryDetailIds;
    }
}
