package com.nspace.group.module.logs.service.delivery;

import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryDetailDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 日志投递详情接口
 *
 * <AUTHOR>
 * @since 2025-04-10 14:32:16
 */
public interface LogDeliveryDetailService {
    List<LogDeliveryDetailDTO> getDetails(String bizType, String domain, Integer batchLimit, LocalDateTime timeLowerLimit, Long curMaxId);

    void updateMany(String bizType, Set<Long> deliveryDetailIds, Integer deliveryStatus);

    void saveMany(String bizType, String domain, Integer deliveryStatus, Map<Long, LocalDateTime> logIdTimeMap);
}

