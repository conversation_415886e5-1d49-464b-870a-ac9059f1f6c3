package com.nspace.group.module.logs.biz.delivery;

import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :LiveDeliveryLogTransformService.java, v0.1 2025年03月19日 11:30 Exp
 */
public interface LiveDeliveryLogTransformService {

    /**
     * 转换推流原始日志数据
     *
     * @param rawLogList 原始日志数据
     * @param targetType 目标类型
     * @return List<Map<String, String>>
     */
    List<Map<String, Object>> transformPushLogs(String targetType, List<OdsBillingLogStreamPushDO> rawLogList);

    /**
     * 转换拉流原始日志数据
     *
     * @param rawLogList 原始日志数据
     * @param targetType 目标类型
     * @return List<Map<String, String>>
     */
    List<Map<String, Object>> transformPullLogs(String targetType, List<OdsBillingLogStreamPullDO> rawLogList);
}
