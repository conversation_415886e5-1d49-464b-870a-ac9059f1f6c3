package com.nspace.group.module.logs.service.offlinelog;

import java.util.List;
import java.util.Map;

/**
 * 离线日志转化 Service 接口
 *
 * <AUTHOR>
 */
public interface OfflineLogTransformService {

    /**
     * 序列化
     *
     * @param logDataList 日志数据
     * @return String
     */
    String serialize(List<Map<String, String>> logDataList);


    /**
     * 压缩
     *
     * @param logDataList 日志数据
     * @return byte[]
     */
    byte[] compress(List<Map<String, String>> logDataList);

    /**
     * 压缩字符串
     *
     * @param str 字符串
     * @return byte[]
     */
    byte[] compress(String str);
}