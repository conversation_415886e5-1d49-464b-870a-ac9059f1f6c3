package com.nspace.group.module.logs.service.offlinelog.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.util.Date;


/**
 * 数据明细-拉流计费数据明细表DTO
 *
 * <AUTHOR>
 * @since 2024-12-18 16:41:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class LiveStreamPullDetailDTO {

    @JsonIgnore
    private Long id;
    //日志打印时间, ISO 8601标准格式
    @JsonIgnore
    private Date logTime;

    private String timeIso8601;
    private Long msec;
    private String requestId;
    private String remoteAddr;
    private String serverAddr;
    private String requestMethod;
    private String scheme;
    private String host;
    private String requestUri;
    private String uri;
    private Integer status;
    private String httpReferer;
    private Double requestTime;
    private Long bytesReceived;
    private Long bytesSent;
    private Long upstreamBytesReceived = 0L;
    private Double upstreamConnectTime = 0.0;
    private Double upstreamHeaderTime = 0.0;
    private Double upstreamResponseTime = 0.0;
    private Double upstreamFirstByteTime = 0.0;
    private Integer upstreamResponseCode = 200;
    private String via = "-";
    private String cacheLevel = "L1";
    private String chargeBy;
    private String category = "LiveCDN";
    private String hostname = "-";
    private String httpUserAgent;
    private Long framesReceived = 0L;
    private Long framesSent = 0L;
    private String userPin = "-";
    private Double connectTime;
    private Integer discontinuousCount = 0;
    private Integer discontinuousTime = 0;
    private String traceId;
    private Double sourceStreamFps;
    private Long audioFramesReceived;
    private Long audioFramesSent;
    private Integer audiosFpsWarn = 0;
    private Double firstGopSentTime;
    private Long avgGopSize;
    private Double videoGap;
    private String errorDiscription;
    private Long totalSentBytes;
    private String streamStatus;
}

