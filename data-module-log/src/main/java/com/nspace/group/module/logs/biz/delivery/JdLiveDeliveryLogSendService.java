package com.nspace.group.module.logs.biz.delivery;

import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogRecoveryConfigDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryConfigDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :JdLiveDeliveryLogSendService.java, v0.1 2025年03月31日 15:30 Exp
 */
public interface JdLiveDeliveryLogSendService {

    /**
     * 投递原始日志数据
     *
     * @param logData 日志数据
     */
    void send(LogDeliveryConfigDTO deliveryConfig, List<Map<String, Object>> logData);

    /**
     * 投递之前失败的日志数据
     *
     * @param deliveryConfig 投递配置
     * @param logDetails     日志明细
     * @return 是否成功
     */
    boolean send(DeliveryLogRecoveryConfigDTO deliveryConfig, List<DeliveryLogDetailDTO> logDetails);
}
