package com.nspace.group.module.logs.service.delivery.dto;

import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 日志投递任务返回数据DTO
 *
 * <AUTHOR>
 */
public class LogDeliveryTaskReturnDTO {

    // 业务类型
    private final String bizType;

    // 域名
    private final String domain;

    // 投递状态
    private final Integer deliveryStatus;

    // 日志数据ID、logTime Map
    private final Map<Long, LocalDateTime> logIdTimeMap;

    private final String taskKey;

    private final Object sendRes;

    public LogDeliveryTaskReturnDTO(String bizType, String domain, Integer deliveryStatus, Map<Long, LocalDateTime> logIdTimeMap, String taskKey, Object sendRes) {
        this.bizType = bizType;
        this.domain = domain;
        this.deliveryStatus = LogDeliveryStatusEnum.STALE.isSelf(deliveryStatus) ? LogDeliveryStatusEnum.FAILED.getStatus() : deliveryStatus;
        this.logIdTimeMap = logIdTimeMap;
        this.taskKey = taskKey;
        this.sendRes = sendRes;
    }

    public String getDomain() {
        return domain;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public String getTaskKey() {
        return taskKey;
    }

    public String getBizType() {
        return bizType;
    }

    public Map<Long, LocalDateTime> getLogIdTimeMap() {
        return logIdTimeMap;
    }

    public Object getSendRes() {
        return sendRes;
    }
}
