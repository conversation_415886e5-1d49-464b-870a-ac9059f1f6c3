package com.nspace.group.module.logs.biz.delivery;

import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class LiveDeliveryLogTransformServiceImpl implements LiveDeliveryLogTransformService {

    @Resource
    private JdLiveDeliveryLogTransformService jdLiveDeliveryLogTransformService;

    @Override
    public List<Map<String, Object>> transformPushLogs(String targetType, List<OdsBillingLogStreamPushDO> rawLogList) {
        if (LogDeliveryTargetEnum.LIVE_JD.isSelf(targetType)) {
            return jdLiveDeliveryLogTransformService.transformPushLogs(rawLogList);
        }
        throw new RuntimeException("unknown_log_delivery_target,target=" + targetType);
    }

    @Override
    public List<Map<String, Object>> transformPullLogs(String targetType, List<OdsBillingLogStreamPullDO> rawLogList) {
        if (LogDeliveryTargetEnum.LIVE_JD.isSelf(targetType)) {
            return jdLiveDeliveryLogTransformService.transformPullLogs(rawLogList);
        }
        throw new RuntimeException("unknown_log_delivery_target,target=" + targetType);
    }
}
