package com.nspace.group.module.logs.convert.offlinelog;

import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveDomainDO;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogFileSaveDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogInfoDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogLiveDomainDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version :OfflineLogInfoConvert.java, v0.1 2024年12月17日 10:21 zhangxin Exp
 */
@Mapper
public interface OfflineLogInfoConvert {

    String defaultBucket = "live";

    OfflineLogInfoConvert INSTANCE = Mappers.getMapper(OfflineLogInfoConvert.class);

    @Mapping(target = "maxId", ignore = true)
    @Mapping(target = "fileSize", source = "fileSaveDTO.size")
    @Mapping(target = "url", expression = "java(String.join(com.baomidou.mybatisplus.core.toolkit.StringPool.SLASH, endpointUrl, fileSaveDTO.getBucket(), fileSaveDTO.getPath()))")
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "fileName", source = "fileSaveDTO.name")
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "createTime", expression = "java(LocalDateTime.now())")
    OfflineLogInfoDO fromFileSaveDTO(OfflineLogFileSaveDTO fileSaveDTO, String endpointUrl);

    OfflineLogInfoDTO toOfflineLogInfoDTO(OfflineLogInfoDO logInfoDO);

    @Mapping(target = "maxId", ignore = true)
    @Mapping(target = "url", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "fileSize", ignore = true)
    @Mapping(target = "fileName", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "endTime", expression = "java(java.time.LocalDateTime.now().minusHours(3).truncatedTo(java.time.temporal.ChronoUnit.MINUTES))")
    OfflineLogInfoDTO newDTO(OfflineLogLiveDomainDTO tenantDomain);

    @Mapping(target = "curTimestampStart", ignore = true)
    @Mapping(target = "curTimestampEnd", ignore = true)
    @Mapping(target = "pageSize", source = "batchSize")
    @Mapping(target = "pageNo", ignore = true)
    @Mapping(target = "minId", source = "logInfoDTO.maxId")
    StreamDetailReqDTO getStreamDetailReqDTO(OfflineLogInfoDTO logInfoDTO, LocalDateTime startTime, int batchSize);

    @Mapping(target = "path", ignore = true)
    @Mapping(target = "name", source = "fileName")
    @Mapping(target = "size", ignore = true)
    @Mapping(target = "bucket", constant = defaultBucket)
    @Mapping(target = "content", ignore = true)
    OfflineLogFileSaveDTO toFileSaveDTO(OfflineLogInfoDTO logInfoDTO, String fileName);

    @Mapping(target = "status", ignore = true)
    @Mapping(target = "path", ignore = true)
    @Mapping(target = "name", source = "fileName")
    @Mapping(target = "size", expression = "java(Long.valueOf(content.length))")
    @Mapping(target = "bucket", constant = defaultBucket)
    OfflineLogFileSaveDTO fromReqDTO(StreamDetailReqDTO reqDTO, String fileName, byte[] content);

    OfflineLogLiveDomainDTO getLiveDomainDTO(LiveDomainDO domainDO);

    List<OfflineLogLiveDomainDTO> getLiveDomainDTOList(List<LiveDomainDO> domainDOList);

    @Mapping(target = "startTime", source = "startTime")
    @Mapping(target = "minId", source = "maxId")
    StreamDetailReqDTO newStreamDetailReqDTO(StreamDetailReqDTO reqDTO, LocalDateTime startTime, Long maxId);

    @Mapping(target = "url", constant = "")
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "maxId", ignore = true)
    @Mapping(target = "fileSize", ignore = true)
    @Mapping(target = "fileName", constant = "")
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    OfflineLogInfoDO getDOFromReqDTO(StreamDetailReqDTO reqDTO, Integer status);
}
