package com.nspace.group.module.logs.service.delivery;

import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryParamDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;

/**
 * CDN日志投递记录服务接口
 *
 * <AUTHOR>
 * @since 2025-03-18 14:32:16
 */
public interface CdnLogDeliveryRecordService {

    /**
     * 获取最近的投递记录，如果不存在则生成新记录（时间、数量等字段置空）
     *
     * @param paramDTO 查询参数
     * @return 投递记录
     */
    LogDeliveryRecordDTO getCurrentDeliveryRecord(LogDeliveryParamDTO paramDTO);

    void save(LogDeliveryRecordDTO deliveryRecord);
}

