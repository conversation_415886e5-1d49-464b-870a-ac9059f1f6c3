package com.nspace.group.module.logs.service.delivery;


import com.nspace.group.module.logs.service.delivery.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version :LogDeliveryLogFetchService.java, v0.1 2025年03月19日 10:52 Exp
 */
public interface LogDeliveryLogFetchService {

    /**
     * 根据日志投递明细列表获取原始日志数据并根据logLimit拆分数据
     *
     * @param delayedConfigDTO 请求参数
     * @return List<LogDeliveryDelayedTaskDTO>
     */
    List<LogDeliveryDelayedTaskDTO> fetchDelayedLogs(LogDeliveryDelayedConfigDTO delayedConfigDTO);

    /**
     * 获取原始日志数据并根据logLimit拆分数据
     * @param deliveryConfig 请求参数
     * @return LogDeliveryTasksRunDTO
     */
    LogDeliveryTasksRunDTO fetchLogBatch(LogDeliveryConfigDTO deliveryConfig);
}
