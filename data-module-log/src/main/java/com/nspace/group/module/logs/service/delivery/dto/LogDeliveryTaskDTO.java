package com.nspace.group.module.logs.service.delivery.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 日志投递任务DTO
 *
 * <AUTHOR>
 */
public class LogDeliveryTaskDTO {

    // 日志数据
    private final List<Map<String, Object>> logList;

    private final LogDeliveryRecordDTO deliveryRecord;

    private final Map<Long, LocalDateTime> logIdTimeMap;

    public LogDeliveryTaskDTO(List<Map<String, Object>> logList, LogDeliveryRecordDTO deliveryRecord, Map<Long, LocalDateTime> logIdTimeMap) {
        this.logList = logList;
        this.deliveryRecord = deliveryRecord;
        this.logIdTimeMap = logIdTimeMap;
    }

    public List<Map<String, Object>> getLogList() {
        return logList;
    }

    public LogDeliveryRecordDTO getDeliveryRecord() {
        return deliveryRecord;
    }

    public Map<Long, LocalDateTime> getLogIdTimeMap() {
        return logIdTimeMap;
    }
}
