package com.nspace.group.module.logs.biz.delivery.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.openservices.aliyun.log.producer.Producer;
import com.aliyun.openservices.aliyun.log.producer.Result;
import com.aliyun.openservices.aliyun.log.producer.errors.ResultFailedException;
import com.aliyun.openservices.log.common.LogItem;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.logs.biz.delivery.AliCdnDeliveryLogSendService;
import com.nspace.group.module.logs.biz.delivery.dto.AliCdnLogDeliveryResult;
import com.nspace.group.module.logs.convert.delivery.GeneralCdnLogConvert;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.service.delivery.LogDeliveryDetailService;
import com.nspace.group.module.logs.service.delivery.LogDeliveryRecordService;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryConfigDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :AliCdnDeliveryLogSendServiceImpl.java, v0.1 2025年04月22日 15:36 Exp
 */
@Service
@Slf4j
public class AliCdnDeliveryLogSendServiceImpl implements AliCdnDeliveryLogSendService {

    @Resource
    private Producer logProducer;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private LogDeliveryRecordService logDeliveryRecordService;

    @Resource
    private LogDeliveryDetailService logDeliveryDetailService;

    @Override
    public Object send(LogDeliveryConfigDTO deliveryConfig, List<Map<String, Object>> logData, Map<Long, LocalDateTime> logIdTimeMap, String taskKey) {
        LogDeliveryRecordDTO deliveryRecord = deliveryConfig.getDeliveryRecord();

        String project = deliveryConfig.getProject();
        String logStore = deliveryConfig.getLogStore();
        List<LogItem> logItems = GeneralCdnLogConvert.INSTANCE.getCdnAliLogItemList(logData);

        String errorMsg;
        ListenableFuture<Result> f = null;
        try {
            log.info("AliCdnDeliveryLogSendServiceImpl.send,start,project={},logStore={},logItems={}", project, logStore, JsonUtils.toJsonString(logItems));
            deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.INIT.getStatus());
            logDeliveryRecordService.save(deliveryRecord);
            f = logProducer.send(project, logStore, logItems);
            Futures.addCallback(f, new LogSendFutureCallback(project, logStore, deliveryConfig, logIdTimeMap, taskKey), MoreExecutors.directExecutor());
        } catch (Exception e) {
            errorMsg = ExceptionUtil.getRootCauseMessage(e);
            log.error("AliCdnDeliveryLogSendServiceImpl.send,unknown_exception,{},project={},logStore={},error={}",
                    deliveryRecord, project, logStore, errorMsg);
            deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.STALE.getStatus());
            deliveryRecord.setDeliveryResult(errorMsg);
            logDeliveryRecordService.save(deliveryRecord);
            //保存日志投递明细
            String bizType = deliveryRecord.getBizType();
            String domain = deliveryRecord.getDomain();
            logDeliveryDetailService.saveMany(bizType, domain, deliveryRecord.getDeliveryStatus(), logIdTimeMap);
            if (StrUtil.isNotBlank(taskKey)) {
                log.info("AliCdnDeliveryLogSendServiceImpl.send,remove_log_delivery_task_from_registry,task={}", taskKey);
                stringRedisTemplate.delete(taskKey);
            }
        }
        return f;
    }

    private final class LogSendFutureCallback implements FutureCallback<Result> {

        private final String project;

        private final String logStore;

        private final LogDeliveryConfigDTO deliveryConfig;

        private final Map<Long, LocalDateTime> logIdTimeMap;

        private final String taskKey;

        LogSendFutureCallback(
                String project, String logStore,
                LogDeliveryConfigDTO deliveryConfig,
                Map<Long, LocalDateTime> logIdTimeMap, String taskKey) {
            this.project = project;
            this.logStore = logStore;
            this.logIdTimeMap = logIdTimeMap;
            this.deliveryConfig = deliveryConfig;
            this.taskKey = taskKey;
        }

        @Override
        public void onSuccess(@Nullable Result result) {
            LogDeliveryRecordDTO deliveryRecord = deliveryConfig.getDeliveryRecord();
            String targetType = deliveryConfig.getTargetType();
            String apiBaseUrl = deliveryConfig.getApiBaseUrl();

            int retryCount;
            String resultString = "success";
            Integer deliveryStatus = LogDeliveryStatusEnum.SUCCESS.getStatus();
            try {
                if (result != null) {
                    retryCount = result.getAttemptCount();
                    deliveryRecord.setRetryCount(retryCount);
                    AliCdnLogDeliveryResult deliveryResult = GeneralCdnLogConvert.INSTANCE.getAliCdnLogDeliveryResult(result);
                    if (!result.isSuccessful()) {
                        deliveryStatus = LogDeliveryStatusEnum.STALE.getStatus();
                    }
                    log.info("AliCdnDeliveryLogSendServiceImpl.LogSendFutureCallback,onSuccess,log_delivery_status={},target={},{},apiBaseUrl={},project={},logStore={},retry_count={}",
                            deliveryStatus, targetType, deliveryRecord, apiBaseUrl, project, logStore, retryCount);
                    resultString = JsonUtils.toJsonString(deliveryResult);
                }
                deliveryRecord.setDeliveryStatus(deliveryStatus);
                deliveryRecord.setDeliveryResult(resultString);
                logDeliveryRecordService.save(deliveryRecord);
                //保存日志投递明细
                String bizType = deliveryRecord.getBizType();
                String domain = deliveryRecord.getDomain();
                logDeliveryDetailService.saveMany(bizType, domain, deliveryStatus, logIdTimeMap);
            } catch (Exception ignored) {
            } finally {
                if (StrUtil.isNotBlank(taskKey)) {
                    log.info("AliCdnDeliveryLogSendServiceImpl.LogSendFutureCallback,onSuccess,remove_log_delivery_task_from_registry,task={}", taskKey);
                    stringRedisTemplate.delete(taskKey);
                }
            }
        }

        @Override
        public void onFailure(Throwable t) {
            LogDeliveryRecordDTO deliveryRecord = deliveryConfig.getDeliveryRecord();
            String apiBaseUrl = deliveryConfig.getApiBaseUrl();
            deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.STALE.getStatus());
            int retryCount = 0;
            String resultString = StringPool.EMPTY;
            if (t instanceof ResultFailedException) {
                Result result = ((ResultFailedException) t).getResult();
                log.error("AliCdnDeliveryLogSendServiceImpl.LogSendFutureCallback,onFailure,delivery_failure,{},apiBaseUrl={},project={},logStore={}",
                        deliveryRecord, apiBaseUrl, project, logStore);
                if (result != null) {
                    retryCount = result.getAttemptCount();
                    resultString = JsonUtils.toJsonString(GeneralCdnLogConvert.INSTANCE.getAliCdnLogDeliveryResult(result));
                }
            } else {
                log.error("AliCdnDeliveryLogSendServiceImpl.LogSendFutureCallback,onFailure,unknown_error,{},apiBaseUrl={},project={},logStore={}, e={}",
                        deliveryRecord, apiBaseUrl, project, logStore, t);
                resultString = ExceptionUtil.getRootCauseMessage(t);
            }
            deliveryRecord.setRetryCount(retryCount);
            deliveryRecord.setDeliveryResult(resultString);
            try {
                logDeliveryRecordService.save(deliveryRecord);
                //保存日志投递明细
                String bizType = deliveryRecord.getBizType();
                String domain = deliveryRecord.getDomain();
                logDeliveryDetailService.saveMany(bizType, domain, deliveryRecord.getDeliveryStatus(), logIdTimeMap);
            } catch (Exception ignored) {
            } finally {
                if (StrUtil.isNotBlank(taskKey)) {
                    log.info("AliCdnDeliveryLogSendServiceImpl.LogSendFutureCallback,onFailure,remove_log_delivery_task_from_registry,task={}", taskKey);
                    stringRedisTemplate.delete(taskKey);
                }
            }
        }
    }
}
