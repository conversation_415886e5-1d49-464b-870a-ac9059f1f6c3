package com.nspace.group.module.logs.convert.delivery;

import com.nspace.group.module.logs.dal.dataobject.delivery.CdnLogDeliveryRecordDO;
import com.nspace.group.module.logs.dal.dataobject.delivery.LiveLogDeliveryRecordDO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryParamDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :LogDeliveryRecordConvert.java, v0.1 2025年03月19日 10:21 zhangxin Exp
 */
@Mapper
public interface LogDeliveryRecordConvert {

    LogDeliveryRecordConvert INSTANCE = Mappers.getMapper(LogDeliveryRecordConvert.class);

    LogDeliveryRecordDTO fromLiveRecordDO(LiveLogDeliveryRecordDO deliveryRecord);

    LogDeliveryRecordDTO fromCdnRecordDO(CdnLogDeliveryRecordDO deliveryRecord);

    @Mapping(target = "retryCount", ignore = true)
    @Mapping(target = "logType", source = "type")
    @Mapping(target = "logStartTime", ignore = true)
    @Mapping(target = "logStartId", ignore = true)
    @Mapping(target = "logEndTime", expression = "java(LocalDateTime.now(java.time.ZoneOffset.ofHours(8)).minusDays(1).truncatedTo(java.time.temporal.ChronoUnit.SECONDS))")
    @Mapping(target = "logEndId", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deliveryStatus", ignore = true)
    @Mapping(target = "deliveryResult", ignore = true)
    @Mapping(target = "deliveryCount", ignore = true)
    @Mapping(target = "actualCount", ignore = true)
    LogDeliveryRecordDTO getInitialRecordDTO(LogDeliveryParamDTO paramDTO);

    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    LiveLogDeliveryRecordDO getLiveRecordDO(LogDeliveryRecordDTO recordDTO);

    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    CdnLogDeliveryRecordDO getCdnRecordDO(LogDeliveryRecordDTO recordDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "logStartTime", ignore = true)
    @Mapping(target = "logStartId", ignore = true)
    @Mapping(target = "logEndId", ignore = true)
    @Mapping(target = "retryCount", ignore = true)
    @Mapping(target = "actualCount", ignore = true)
    @Mapping(target = "deliveryStatus", ignore = true)
    @Mapping(target = "deliveryResult", ignore = true)
    @Mapping(target = "deliveryCount", ignore = true)
    LogDeliveryRecordDTO getNextRecordDTO(LogDeliveryRecordDTO previousRecord);

    @Mapping(target = "retryCount", ignore = true)
    @Mapping(target = "logStartTime", source = "startTime")
    @Mapping(target = "logEndTime", source = "endTime")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deliveryStatus", ignore = true)
    @Mapping(target = "deliveryResult", ignore = true)
    @Mapping(target = "deliveryCount", source = "logSize")
    @Mapping(target = "actualCount", ignore = true)
    LogDeliveryRecordDTO getRecordDTOForBatch(Long tenantId, String domain, String bizType, String logType,
                                              Long logStartId, Long logEndId, LocalDateTime startTime, LocalDateTime endTime, Long logSize);

}
