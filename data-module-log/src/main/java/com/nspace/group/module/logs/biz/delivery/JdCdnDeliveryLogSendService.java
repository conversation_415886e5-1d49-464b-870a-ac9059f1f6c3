package com.nspace.group.module.logs.biz.delivery;

import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryConfigDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :JdCdnDeliveryLogSendService.java, v0.1 2025年03月31日 15:30 Exp
 */
public interface JdCdnDeliveryLogSendService {

    /**
     * 投递原始日志数据
     *
     * @param logData      日志数据
     * @return 投递结果
     */
    Object send(LogDeliveryConfigDTO deliveryConfig, List<Map<String, Object>> logData);
}
