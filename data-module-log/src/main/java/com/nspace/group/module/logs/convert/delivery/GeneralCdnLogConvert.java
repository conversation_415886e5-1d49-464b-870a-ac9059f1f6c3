package com.nspace.group.module.logs.convert.delivery;

import cn.hutool.core.util.StrUtil;
import com.aliyun.openservices.aliyun.log.producer.Attempt;
import com.aliyun.openservices.aliyun.log.producer.Result;
import com.aliyun.openservices.log.common.LogItem;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.module.infra.dal.dataobject.cdn.OdsGeneralCdnRequestLogDO;
import com.nspace.group.module.logs.biz.delivery.dto.AliCdnLogDeliveryResult;
import com.nspace.group.module.logs.service.delivery.dto.AliCdnLogDetailDTO;
import com.nspace.group.module.logs.service.delivery.dto.JdCdnLogDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :GeneralCdnLogConvert.java, v0.1 2025年03月19日 10:21 zhangxin Exp
 */
@Mapper
public interface GeneralCdnLogConvert {

    DateTimeFormatter logTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssxxx");

    GeneralCdnLogConvert INSTANCE = Mappers.getMapper(GeneralCdnLogConvert.class);
    JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    @Mapping(target = "upstreamResponseTime", ignore = true)
    @Mapping(target = "upstreamResponseCode", ignore = true)
    @Mapping(target = "upstreamHeaderTime", ignore = true)
    @Mapping(target = "upstreamFirstByteTime", ignore = true)
    @Mapping(target = "upstreamConnectTime", ignore = true)
    @Mapping(target = "upstreamBytesReceived", ignore = true)
    @Mapping(target = "upstreamAddr", ignore = true)
    @Mapping(target = "timeIso8601", source = "logTime")
    @Mapping(target = "tcpinfoSndCwnd", ignore = true)
    @Mapping(target = "tcpinfoRttvar", ignore = true)
    @Mapping(target = "tcpinfoRtt", ignore = true)
    @Mapping(target = "tcpinfoRcvSpace", ignore = true)
    @Mapping(target = "status", source = "returnCode")
    @Mapping(target = "sslEarlyData", ignore = true)
    @Mapping(target = "serverProtocol", source = "serverProtocol")
    @Mapping(target = "serverName", source = "domain")
    @Mapping(target = "sentHttpLocation", ignore = true)
    @Mapping(target = "sentHttpLastModified", ignore = true)
    @Mapping(target = "sentHttpContentType", ignore = true)
    @Mapping(target = "sentHttpContentLength", ignore = true)
    @Mapping(target = "sentHttpContentEncoding", ignore = true)
    @Mapping(target = "requestUri", source = "uriParam")
    @Mapping(target = "requestMethod", source = "method")
    @Mapping(target = "requestLength", source = "requestSize")
    @Mapping(target = "remoteAddr", source = "clientAddr")
    @Mapping(target = "pin", ignore = true)
    @Mapping(target = "msec", expression = "java(rawLog.getLogTime().toInstant(java.time.ZoneOffset.ofHours(8)).toEpochMilli())")
    @Mapping(target = "mgoPlainUrl", ignore = true)
    @Mapping(target = "mgoConnection", ignore = true)
    @Mapping(target = "macAddr", ignore = true)
    @Mapping(target = "isFinish", ignore = true)
    @Mapping(target = "httpXForwardedFor", source = "xforwordfor")
    @Mapping(target = "httpUserAgent", source = "userAgent")
    @Mapping(target = "httpReferer", source = "httpReferer")
    @Mapping(target = "hostname", ignore = true)
    @Mapping(target = "host", source = "domain")
    @Mapping(target = "hitBytes", ignore = true)
    @Mapping(target = "domainType", ignore = true)
    @Mapping(target = "dnsView", ignore = true)
    @Mapping(target = "dataCenter", ignore = true)
    @Mapping(target = "connection", ignore = true)
    @Mapping(target = "chargeBy", ignore = true)
    @Mapping(target = "cdnCharge", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "cacheStatus", source = "hitInfo")
    @Mapping(target = "cacheLevel", ignore = true)
    @Mapping(target = "bytesSent", source = "responseSize")
    @Mapping(target = "bytesSendUntil", ignore = true)
    @Mapping(target = "bytesReceived", ignore = true)
    @Mapping(target = "serverAddr", expression = "java(rawLog.getServerAddr() + \":\" + rawLog.getServerPort())")
    @Mapping(target = "requestTime", expression = "java(new java.math.BigDecimal(rawLog.getRequestTime()).divide(new java.math.BigDecimal(\"1000\"), 3, java.math.RoundingMode.HALF_DOWN).doubleValue())")
    JdCdnLogDetailDTO getJdCdnLogDetailDTO(OdsGeneralCdnRequestLogDO rawLog);

    List<JdCdnLogDetailDTO> getJdCdnLogDetailDTOList(List<OdsGeneralCdnRequestLogDO> rawLogList);

    @Mapping(target = "uriParam", source = "uriParam", defaultValue = "-")
    @Mapping(target = "uri", source = "uri", defaultValue = "-")
    @Mapping(target = "serverPort", source = "serverPort", defaultValue = "0")
    @Mapping(target = "serverAddr", source = "serverAddr", defaultValue = "-")
    @Mapping(target = "sentHttpContentRange", source = "sentHttpContentRange", defaultValue = "")
    @Mapping(target = "returnCode", source = "returnCode", defaultValue = "200")
    @Mapping(target = "responseSize", source = "responseSize", defaultValue = "0L")
    @Mapping(target = "requestTime", source = "requestTime", defaultValue = "0L")
    @Mapping(target = "requestSize", source = "requestSize", defaultValue = "0L")
    @Mapping(target = "remotePort", source = "remotePort", defaultValue = "0")
    @Mapping(target = "method", source = "method", defaultValue = "-", qualifiedByName = "toUpperCase")
    @Mapping(target = "httpRange", source = "httpRange", defaultValue = "-")
    @Mapping(target = "hitInfo", source = "hitInfo", defaultValue = "-", qualifiedByName = "toUpperCase")
    @Mapping(target = "domain", source = "domain", defaultValue = "-")
    @Mapping(target = "bodyBytesSent", source = "bodyBytesSent", defaultValue = "0L")
    @Mapping(target = "viaInfo", source = "via", defaultValue = "-")
    @Mapping(target = "uuid", source = "requestId", defaultValue = "-")
    @Mapping(target = "unixtime", expression = "java(rawLog.getLogTime().toInstant(java.time.ZoneOffset.ofHours(8)).getEpochSecond())")
    @Mapping(target = "station", constant = "3rdgengyun")
    @Mapping(target = "scheme", source = "scheme", defaultValue = "-")
    @Mapping(target = "sentHttpContentLength", source = "sentHttpContentLength", defaultValue = "0L")
    @Mapping(target = "responseFbtTime", source = "responseFbtTime", defaultValue = "0L")
    @Mapping(target = "referer", source = "httpReferer", defaultValue = "-")
    @Mapping(target = "quic", source = "quic", defaultValue = "-")
    @Mapping(target = "lastModified", source = "lastModified", defaultValue = "-")
    @Mapping(target = "httpXForwardedFor", source = "xforwordfor")
    @Mapping(target = "httpVersion", source = "serverProtocol", defaultValue = "-")
    @Mapping(target = "dyUserInfo", source = "dyUserInfo", defaultValue = "-")
    @Mapping(target = "country", source = "country", defaultValue = "-", qualifiedByName = "toUpperCase")
    @Mapping(target = "connectIp", source = "clientAddr")
    @Mapping(target = "contentType", source = "contentType", defaultValue = "-")
    @Mapping(target = "userAgent", source = "userAgent", defaultValue = "-")
    AliCdnLogDetailDTO getAliCdnLogDetailDTO(OdsGeneralCdnRequestLogDO rawLog);

    List<AliCdnLogDetailDTO> getAliCdnLogDetailDTOList(List<OdsGeneralCdnRequestLogDO> rawLogList);

    default List<Map<String, Object>> getCdnJDLogMapList(List<OdsGeneralCdnRequestLogDO> rawLogList) {
        if (rawLogList.isEmpty()) return Collections.emptyList();
        List<JdCdnLogDetailDTO> logDTOList = getJdCdnLogDetailDTOList(rawLogList);
        TypeFactory typeFactory = jsonMapper.getTypeFactory();
        TypeReference<HashMap<String, Object>> typeRef = new TypeReference<>() {
        };
        JavaType javaType = typeFactory.constructType(typeRef);
        return jsonMapper.convertValue(logDTOList, typeFactory.constructCollectionType(List.class, javaType));
    }

    default List<Map<String, Object>> getCdnAliLogMapList(List<OdsGeneralCdnRequestLogDO> rawLogList) {
        if (rawLogList.isEmpty()) return Collections.emptyList();
        List<AliCdnLogDetailDTO> logDTOList = getAliCdnLogDetailDTOList(rawLogList);
        TypeFactory typeFactory = jsonMapper.getTypeFactory();
        TypeReference<HashMap<String, Object>> typeRef = new TypeReference<>() {
        };
        JavaType javaType = typeFactory.constructType(typeRef);
        return jsonMapper.convertValue(logDTOList, typeFactory.constructCollectionType(List.class, javaType));
    }

    default List<LogItem> getCdnAliLogItemList(List<Map<String, Object>> logData) {
        if (logData.isEmpty()) return Collections.emptyList();

        return logData.stream().map(logEntryAsMap -> {
            LogItem logItem = new LogItem(Integer.parseInt(logEntryAsMap.get("unixtime").toString()));
            logEntryAsMap.forEach((key, value) -> logItem.PushBack(key, value.toString()));
            return logItem;
        }).collect(Collectors.toList());
    }

    @Mapping(target = "lastAttempt", qualifiedByName = "attemptsConvert", source = "reservedAttempts")
    @Mapping(target = "successful", expression = "java(result.isSuccessful())")
    AliCdnLogDeliveryResult getAliCdnLogDeliveryResult(Result result);

    @Named("attemptsConvert")
    default String getLastAttemptAsString(List<Attempt> attempts) {
        if (attempts == null || attempts.isEmpty()) {
            return StringPool.EMPTY;
        }
        return attempts.stream().map(Attempt::toString)
                .skip(attempts.size() - 1).findFirst().get();
    }

    @Named("toUpperCase")
    default String toUpperCase(String value) {
        if (value == null || "".equals(value) || "-".equals(value)) {
            return "-";
        }
        return value.toUpperCase();
    }
}
