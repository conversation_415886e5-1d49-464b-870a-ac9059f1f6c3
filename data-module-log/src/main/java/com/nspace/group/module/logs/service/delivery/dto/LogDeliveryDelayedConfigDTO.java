package com.nspace.group.module.logs.service.delivery.dto;

import cn.hutool.core.lang.Assert;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;

import java.util.List;

/**
 * 延迟日志查询参数DTO
 *
 * <AUTHOR>
 */
public class LogDeliveryDelayedConfigDTO {

    // 多租户编号
    private final Long tenantId;

    // 日志类型 云直播：PUSH、PULL 通用CDN：GENERAL
    private final String type;

    // 日志投递目标
    private final String targetType;

    // 业务类型
    private final String bizType;

    // 域名
    private final String domain;


    // 日志详情列表
    private List<LogDeliveryDetailDTO> deliveryDetails;

    // 单次投递数量限制
    private final Integer logLimit;

    // 双方约定的唯一标识
    private final String privateKey;
    // ak
    private final String accessKey;

    // 接口path
    private final String apiPath;

    // 接口基础Url
    private final String apiBaseUrl;

    //------阿里CDN日志投递相关------
    // 日志项目
    private final String project;

    // 日志存储
    private final String logStore;

    public LogDeliveryDelayedConfigDTO(Long tenantId, String type, String targetType, String bizType,
                                       String domain, List<LogDeliveryDetailDTO> deliveryDetails, Integer logLimit,
                                       String privateKey, String accessKey,
                                       String apiPath, String apiBaseUrl, String project, String logStore) {
        Assert.notBlank(targetType);
        Assert.notBlank(privateKey);
        Assert.notBlank(accessKey);
        if (!LogDeliveryTargetEnum.CDN_ALI.isSelf(targetType)) {
            Assert.notBlank(apiPath);
        }
        Assert.notBlank(apiBaseUrl);
        this.type = type;
        this.tenantId = tenantId;
        this.bizType = bizType;
        this.domain = domain;
        this.targetType = targetType;
        this.deliveryDetails = deliveryDetails;
        this.logLimit = logLimit;
        this.privateKey = privateKey;
        this.accessKey = accessKey;
        this.apiPath = apiPath;
        this.apiBaseUrl = apiBaseUrl;
        this.project = project;
        this.logStore = logStore;
    }

    public String getTargetType() {
        return targetType;
    }

    public Integer getLogLimit() {
        return logLimit;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public String getApiPath() {
        return apiPath;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    public String getDomain() {
        return domain;
    }

    public String getBizType() {
        return bizType;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public String getType() {
        return type;
    }

    public List<LogDeliveryDetailDTO> getDeliveryDetails() {
        return deliveryDetails;
    }

    public void setDeliveryDetails(List<LogDeliveryDetailDTO> deliveryDetails) {
        this.deliveryDetails = deliveryDetails;
    }

    public String getProject() {
        return project;
    }

    public String getLogStore() {
        return logStore;
    }
}
