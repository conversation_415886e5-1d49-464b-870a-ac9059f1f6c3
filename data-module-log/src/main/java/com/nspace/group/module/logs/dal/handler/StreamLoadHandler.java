package com.nspace.group.module.logs.dal.handler;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.nspace.group.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultRedirectStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;

/**
 * Doris Stream Load 数据同步
 * <a href="https://github.com/apache/doris/discussions/13888">详见此链接</a>
 *
 * <AUTHOR>
 * @version :StreamLoadHandler.java, v0.1 2025年04月28日 10:25 zhangxin Exp
 */
@Slf4j
@Component(value = "nspaceLogStreamLoader")
public class StreamLoadHandler {
    private static final Integer dorisFePort = 8030;
    private static final String dorisDb = "nspace_log";
    private static final String streamLoadUrlTemplate = "http://%s:%s/api/%s/%s/_stream_load";

    private final String host;

    private final String dorisUsername;
    private final String dorisPassword;

    private final HttpClientBuilder httpClientBuilder = HttpClients
            .custom()
            .setRedirectStrategy(new DefaultRedirectStrategy() {
                @Override
                protected boolean isRedirectable(String method) {
                    return true;
                }
            });

    public StreamLoadHandler(@Value("${spring.datasource.dynamic.datasource.doris_nspace_log.url}") String dorisUrl,
                             @Value("${spring.datasource.dynamic.datasource.doris_nspace_log.username}") String username,
                             @Value("${spring.datasource.dynamic.datasource.doris_nspace_log.password}") String password) {
        this.dorisUsername = username;
        this.dorisPassword = password;
        String jdbcUrlPrefix = dorisUrl.startsWith("**********************:") ? "jdbc:mysql:" : "jdbc:";
        URI uri = URI.create(dorisUrl.substring(jdbcUrlPrefix.length()));
        this.host = uri.getHost();
    }

    public Map<String, String> sendData(String tableName, String content) throws Exception {
        String loadUrl = String.format(streamLoadUrlTemplate, this.host, dorisFePort, dorisDb, tableName);

        try (CloseableHttpClient client = httpClientBuilder.build()) {
            HttpPut put = new HttpPut(loadUrl);
            StringEntity entity = new StringEntity(content, "UTF-8");
            put.setHeader(HttpHeaders.EXPECT, "100-continue");
            put.setHeader("format", "json");
            put.setHeader("strip_outer_array", "true");
            put.setHeader(HttpHeaders.AUTHORIZATION, basicAuthHeader(dorisUsername, dorisPassword));
            put.setEntity(entity);

            try (CloseableHttpResponse response = client.execute(put)) {
                String loadResult = "";
                if (response.getEntity() != null) {
                    loadResult = EntityUtils.toString(response.getEntity());
                }
                final int statusCode = response.getStatusLine().getStatusCode();
                //状态码200表明doris服务正常，要确保数据成功导入需要查看loadResult
                if (statusCode != 200) {
                    throw new IOException(String.format("Stream load failed, statusCode=%s load result=%s", statusCode, loadResult));
                }
                if (StrUtil.isBlank(loadResult)) return Collections.emptyMap();
                return JsonUtils.parseObject(loadResult, new TypeReference<>() {
                });
            }
        }
    }

    private String basicAuthHeader(String username, String password) {
        String tobeEncode = username + ":" + password;
        byte[] encoded = Base64.encodeBase64(tobeEncode.getBytes(StandardCharsets.UTF_8));
        return "Basic " + new String(encoded);
    }
}
