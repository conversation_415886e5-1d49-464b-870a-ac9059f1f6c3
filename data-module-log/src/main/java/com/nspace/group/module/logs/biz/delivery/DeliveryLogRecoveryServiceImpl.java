package com.nspace.group.module.logs.biz.delivery;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogRecoveryConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service(value = "deliveryLogRecoveryService")
public class DeliveryLogRecoveryServiceImpl implements DeliveryLogRecoveryService {

    @Resource(name = "deliveryThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private DeliveryLogDetailService logDetailService;

    @Resource
    private LiveDeliveryLogSendService liveDeliveryLogSendService;

    @Override
    public void recoverLogs(DeliveryLogRecoveryConfigDTO configDTO) {
        String bizType = configDTO.getBizType();
        String targetType = configDTO.getTargetType();
        Integer logLimit = configDTO.getLogLimit();
        Integer batchLimit = configDTO.getBatchLimit();
        String taskKey = getLogRecoveryTaskKey(bizType, targetType);

        try {
            int curCount = 0;
            Integer retryCount = configDTO.getRetryCount();
            //每次间隔10秒持续retryCount次
            int waitTimeMillis = 10_000;
            outer:
            while (curCount <= batchLimit) {
                //计算Redis key过期时间（秒）
                long taskKeyExpiration = retryCount * (waitTimeMillis / 1000 + 10) * 2;
                Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(taskKey, StringPool.ONE, Duration.ofSeconds(taskKeyExpiration));
                //获取锁失败直接返回
                if (Boolean.FALSE.equals(success)) {
                    log.warn("recoverLogs,fail_to_acquire_lock,task_key={},no_op", taskKey);
                    break;
                }
                try {
                    int curRetryCount = 1;
                    while (curRetryCount <= retryCount) {
                        AtomicInteger totalTaskCount = new AtomicInteger(0);
                        ExecutorCompletionService<Boolean> completionService = new ExecutorCompletionService<>(executorService);
                        //获取需要重新消费的日志
                        List<DeliveryLogDetailDTO> logDetails = logDetailService.getDetails(bizType, targetType, null, batchLimit);
                        curCount = logDetails.size();
                        if (curCount == 0) {
                            log.info("recoverLogs,no_logs_to_recover,target_type={},biz_type={}", targetType, bizType);
                            break outer;
                        }
                        int finalLoopRetryCount = curRetryCount;
                        log.info("recoverLogs,retry_count={},target_type={},biz_type={}", finalLoopRetryCount, targetType, bizType);
                        Lists.partition(logDetails, logLimit).forEach(subLogDetails -> {
                            subLogDetails.forEach(subLogDetail -> subLogDetail.setDeliveryTimes(finalLoopRetryCount));
                            completionService.submit(() -> sendLogData(configDTO, subLogDetails));
                            totalTaskCount.getAndIncrement();
                        });

                        Set<Boolean> sendResults = new HashSet<>();
                        while (totalTaskCount.getAndDecrement() > 0) {
                            sendResults.add(completionService.take().get());
                        }
                        if (!sendResults.contains(false)) {
                            log.info("recoverLogs,log_delivered,target_type={},biz_type={}", targetType, bizType);
                            break;
                        }
                        if (curRetryCount != retryCount) {
                            Thread.sleep(waitTimeMillis);
                        }
                        curRetryCount++;
                    }
                } finally {
                    //任务执行结束，删除redis中的key
                    log.info("recoverLogs,remove_task_key_from_registry,task_key={}", taskKey);
                    stringRedisTemplate.delete(taskKey);
                }
                if (curCount < batchLimit) break;
            }
        } catch (Exception e) {
            log.error("recoverLogs,unknown_exception,target_type={},biz_type={},error={}", targetType, bizType, ExceptionUtil.getRootCauseMessage(e));
        }
    }

    private String getLogRecoveryTaskKey(String bizType, String targetType) {
        return String.join(StringPool.COLON, "delivery_log_recovery_task", bizType, targetType);
    }

    private boolean sendLogData(DeliveryLogRecoveryConfigDTO deliveryConfig, List<DeliveryLogDetailDTO> logDetails) {
        String bizType = deliveryConfig.getBizType();
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            return liveDeliveryLogSendService.send(deliveryConfig, logDetails);
        } else {
            log.error("unknown_biz_type,biz_type={},no_op", bizType);
            throw new RuntimeException("unknown_biz_type");
        }
    }
}
