package com.nspace.group.module.logs.biz.delivery;

import com.nspace.group.module.infra.dal.dataobject.cdn.OdsGeneralCdnRequestLogDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :CdnDeliveryLogTransformService.java, v0.1 2025年03月19日 11:30 Exp
 */
public interface CdnDeliveryLogTransformService {

    /**
     * 转换原始日志数据
     *
     * @param rawLogList 原始日志数据
     * @param targetType  目标类型
     * @return List<Map<String, String>>
     */
    List<Map<String, Object>> transformLogs(String targetType, List<OdsGeneralCdnRequestLogDO> rawLogList);
}
