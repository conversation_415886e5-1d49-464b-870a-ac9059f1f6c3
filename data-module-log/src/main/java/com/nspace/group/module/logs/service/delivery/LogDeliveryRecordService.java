package com.nspace.group.module.logs.service.delivery;

import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryParamDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;

import java.util.List;

/**
 * 日志投递记录接口
 *
 * <AUTHOR>
 * @since 2025-03-18 14:32:16
 */
public interface LogDeliveryRecordService {

    /**
     * 获取最近的投递记录，如果不存在则生成新记录（时间、数量等字段置空）
     *
     * @param paramDTO 查询参数
     * @return 投递记录
     */
    LogDeliveryRecordDTO getCurrentDeliveryRecord(LogDeliveryParamDTO paramDTO);

    void save(LogDeliveryRecordDTO deliveryRecord);
}

