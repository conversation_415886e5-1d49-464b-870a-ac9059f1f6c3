package com.nspace.group.module.logs.service.delivery;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class LogDeliveryLogDataServiceImpl implements LogDeliveryLogDataService {

    private static final JsonMapper JSON_MAPPER = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    @Override
    public String serialize(List<Map<String, Object>> logDataList) throws JsonProcessingException {
        return JSON_MAPPER.writeValueAsString(logDataList);

    }

    @Override
    public byte[] compress(List<Map<String, Object>> logDataList) throws JsonProcessingException {
        if (CollectionUtil.isEmpty(logDataList)) return new byte[]{};
        String jsonString = serialize(logDataList);
        return compress(jsonString);
    }


    @Override
    public byte[] compress(String str) {
        if (StrUtil.isBlank(str)) return new byte[]{};
        log.info("LogDeliveryLogDataService.compress,compress_log_json,json={}", str);
        return ZipUtil.gzip(str.getBytes(StandardCharsets.UTF_8));
    }
}
