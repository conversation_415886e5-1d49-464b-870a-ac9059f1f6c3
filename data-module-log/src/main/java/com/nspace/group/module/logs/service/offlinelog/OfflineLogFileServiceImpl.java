package com.nspace.group.module.logs.service.offlinelog;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.file.core.FileClient;
import com.nspace.group.framework.file.core.utils.FileTypeUtils;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogFileSaveDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;


/**
 * 文件上传记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OfflineLogFileServiceImpl implements OfflineLogFileService {

    //日志文件过期天数
    private static final Map<String, String> LOG_FILE_TAGS = Collections.singletonMap("expire", "90");
    //临时日志文件过期天数
    private static final Map<String, String> TEMP_FILE_TAGS = Collections.singletonMap("expire", "10");

    private static final String DEFAULT_BUCKET_POLICY_TMP =
            "{\n" +
            "    \"Statement\": [\n" +
            "        {\n" +
            "            \"Action\": \"s3:GetObject\",\n" +
            "            \"Effect\": \"Allow\",\n" +
            "            \"Principal\": \"*\",\n" +
            "            \"Resource\": \"arn:aws:s3:::%s/*\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"Version\": \"2012-10-17\"\n" +
            "}";


    @Value("${spring.minio.domain}")
    private String minioDomain;

    @Resource
    private FileClient fileClient;

    @Resource
    private OfflineLogInfoService offlineLogInfoService;

    @Override
    @SneakyThrows
    public void createFile(OfflineLogFileSaveDTO fileSaveDTO) {
        int status = 0;
        validateParams(fileSaveDTO);
        String tenantId = fileSaveDTO.getTenantId();
        String domain = fileSaveDTO.getDomain();
        String bucket = fileSaveDTO.getBucket();
        String fileName = fileSaveDTO.getName();
        if (fileSaveDTO.getContent() != null && fileSaveDTO.getContent().length > 0) {
            // 计算默认的 path 名
            fileSaveDTO.setPath(getPathForFile(fileSaveDTO));
            log.info("createFile,upload_log_file,tenant_id={},domain={},bucket={},path={}",
                    tenantId, domain, bucket, fileSaveDTO.getPath());
            try {
                // 上传到文件存储器
                String matchType = FileTypeUtils.getMineType(fileSaveDTO.getContent(), fileName);
                saveFile(fileSaveDTO.getContent(), bucket, fileSaveDTO.getPath(), matchType, LOG_FILE_TAGS);
            } catch (Exception e) {
                log.error("createFile,log_file_upload_error,tenant_id={},domain={},bucket={},path={},error_msg={}",
                        tenantId, domain, bucket, fileSaveDTO.getPath(), e.getLocalizedMessage());
                status = 1;
            }
            fileSaveDTO.setStatus(status);
            // 新增文件上传记录
            offlineLogInfoService.save(fileSaveDTO, getEndpointUrl(minioDomain));
        } else {
            log.warn("createFile,empty_log_file,tenant_id={},domain={},file_name={},no_upload", tenantId, domain, fileName);
        }
    }


    @Override
    public String[] createTempFile(OfflineLogFileSaveDTO fileSaveDTO) {
        validateParams(fileSaveDTO);
        String tenantId = fileSaveDTO.getTenantId();
        String domain = fileSaveDTO.getDomain();
        String bucket = fileSaveDTO.getBucket();
        String fileName = fileSaveDTO.getName();
        Long size = fileSaveDTO.getSize();
        if (fileSaveDTO.getContent() != null && fileSaveDTO.getContent().length > 0) {
            // 计算默认的 path 名
            String pathForFile = getPathForFile(fileSaveDTO);
            fileSaveDTO.setPath(pathForFile);
            log.info("createTempFile,upload_log_file,tenant_id={},domain={},bucket={},path={}",
                    tenantId, domain, bucket, fileSaveDTO.getPath());
            try {
                // 上传到文件存储器
                String matchType = FileTypeUtils.getMineType(fileSaveDTO.getContent(), fileName);
                saveFile(fileSaveDTO.getContent(), bucket, fileSaveDTO.getPath(), matchType, TEMP_FILE_TAGS);
                return new String[]{bucket, pathForFile, size == null ? StringPool.ZERO : size.toString()};
            } catch (Exception e) {
                log.error("createTempFile,log_file_upload_error,tenant_id={},domain={},bucket={},path={},error_msg={}",
                        tenantId, domain, bucket, fileSaveDTO.getPath(), e.getLocalizedMessage());
                throw new RuntimeException("temp_file_create_error");
            }
        } else {
            log.warn("createTempFile,empty_log_file,tenant_id={},domain={},bucket={},file_name={},no_upload", tenantId, domain, bucket, fileName);
            return new String[]{};
        }
    }

    @Override
    public void removeFile(String bucket, String path) {
        try {
            fileClient.delete(bucket, path);
        } catch (Exception e) {
            log.error("file_remove_failed,bucket={},path={}", bucket, path);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void mergeFile(OfflineLogFileSaveDTO fileSaveDTO, List<String[]> fileList) {
        int status = 0;
        validateParams(fileSaveDTO);
        validateSourceInfoList(fileList);
        String tenantId = fileSaveDTO.getTenantId();
        String domain = fileSaveDTO.getDomain();
        String bucket = fileSaveDTO.getBucket();
        String fileName = fileSaveDTO.getName();
        if (fileList != null && fileList.size() > 0) {
            // 计算默认的 path 名
            String pathForFile = getPathForFile(fileSaveDTO);
            fileSaveDTO.setPath(pathForFile);
            //计算合并后文件大小
            AtomicLong fileSizeAtomic = new AtomicLong();
            List<String> tempFileInfoStrings = new ArrayList<>();
            fileList.forEach(fileInfo -> {
                String tempFileSize = fileInfo[2];
                fileSizeAtomic.addAndGet(Long.parseLong(tempFileSize));
                tempFileInfoStrings.add(Arrays.toString(fileInfo));

            });

            fileSaveDTO.setSize(fileSizeAtomic.get());

            String mergedFilesAsStr = tempFileInfoStrings.toString();
            log.info("mergeFile,merge_log_files,tenant_id={},domain={},bucket={},path={},merged_files={}",
                    tenantId, domain, bucket, fileSaveDTO.getPath(), mergedFilesAsStr);
            try {
                // 服务器端合并文件
                fileClient.merge(bucket, pathForFile, LOG_FILE_TAGS, fileList);
            } catch (Exception e) {
                log.error("mergeFile,log_files_merge_error,tenant_id={},domain={},bucket={},path={},merged_files={},error_msg={}",
                        tenantId, domain, bucket, fileSaveDTO.getPath(), mergedFilesAsStr, e.getLocalizedMessage());
                status = 1;
            }
            fileSaveDTO.setStatus(status);
            // 新增文件上传记录
            offlineLogInfoService.save(fileSaveDTO, getEndpointUrl(minioDomain));
        } else {
            log.warn("mergeFile,no_files_to_merge,tenant_id={},domain={},bucket={},file_name={},no_upload", tenantId, domain, bucket, fileName);
        }
    }

    @Override
    public void checkCreateBucket(String bucket) {
        Assert.notBlank(bucket, "bucket名称不能为空");
        try {
            fileClient.checkCreateBucket(bucket, String.format(DEFAULT_BUCKET_POLICY_TMP, bucket));
        } catch (Exception e) {
            log.error("checkCreateBucket,unknown_bucket_create_error,bucket={},err_msg={},ignore", bucket, e.getLocalizedMessage());
        }
    }

    private String getPathForFile(OfflineLogFileSaveDTO fileSaveDTO) {
        if (StrUtil.isEmpty(fileSaveDTO.getPath())) {
            return String.join(StringPool.SLASH,
                    fileSaveDTO.getEndTime().toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE),
                    fileSaveDTO.getName());
        } else {
            return fileSaveDTO.getPath();
        }
    }

    private void saveFile(byte[] content, String bucket, String path, String matchType, Map<String, String> tagMap) throws Exception {
        fileClient.upload(content, bucket, path, matchType, tagMap);
    }


    private String getEndpointUrl(String domain) {
        // 如果已经是 http 或者 https，则不进行拼接
        if (HttpUtil.isHttp(domain) || HttpUtil.isHttps(domain)) {
            return domain;
        }
        return StrUtil.format("http://{}", domain);
    }

    private void validateParams(OfflineLogFileSaveDTO fileSaveDTO) {
        Assert.notBlank(fileSaveDTO.getDomain(), "domain不能为空");
        Assert.notNull(fileSaveDTO.getEndTime(), "endTime不能为空");
        Assert.notNull(fileSaveDTO.getType(), "type不能为空");
        Assert.notBlank(fileSaveDTO.getBucket(), "bucket不能为空");
        Assert.notBlank(fileSaveDTO.getName(), "name不能为空");
    }

    private void validateSourceInfoList(List<String[]> sourceInfoList) {
        if (sourceInfoList != null) {
            boolean anyMatch = sourceInfoList.stream().anyMatch(arr -> null == arr || arr.length < 3);
            if (anyMatch) {
                throw new IllegalArgumentException("sourceInfoList_contain_invalid_element");
            }
        }
    }
}