package com.nspace.group.module.logs.service.delivery;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryRecordConvert;
import com.nspace.group.module.logs.dal.dataobject.delivery.LiveLogDeliveryRecordDO;
import com.nspace.group.module.logs.dal.mapper.delivery.LiveLogDeliveryRecordMapper;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryParamDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 直播日志投递记录服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-03-18 14:32:16
 */
@Service
@DS("nspace_log")
@Validated
public class LiveLogDeliveryRecordServiceImpl implements LiveLogDeliveryRecordService {

    @Resource
    LiveLogDeliveryRecordMapper liveLogDeliveryRecordMapper;

    @Override
    public LogDeliveryRecordDTO getCurrentDeliveryRecord(LogDeliveryParamDTO paramDTO) {
        Long tenantId = paramDTO.getTenantId();
        String domain = paramDTO.getDomain();
        String bizType = paramDTO.getBizType();
        String type = paramDTO.getType();
        QueryWrapper<LiveLogDeliveryRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("tenant_id", "domain", "biz_type", "log_type", "MAX(log_end_time) AS log_end_time")
                .eq("tenant_id", tenantId).eq("domain", domain)
                .eq("biz_type", bizType).eq("log_type", type)
                .groupBy("tenant_id", "domain", "biz_type", "log_type");

        LiveLogDeliveryRecordDO logDeliveryRecord = liveLogDeliveryRecordMapper.selectOne(queryWrapper);
        return logDeliveryRecord != null ? LogDeliveryRecordConvert.INSTANCE.fromLiveRecordDO(logDeliveryRecord)
                : LogDeliveryRecordConvert.INSTANCE.getInitialRecordDTO(paramDTO);
    }

    @Override
    public void save(LogDeliveryRecordDTO deliveryRecord) {
        Assert.notNull(deliveryRecord);
        LiveLogDeliveryRecordDO recordDO = LogDeliveryRecordConvert.INSTANCE.getLiveRecordDO(deliveryRecord);
        if (recordDO.getId() == null) {
            liveLogDeliveryRecordMapper.insert(recordDO);
            deliveryRecord.setId(recordDO.getId());
        } else {
            liveLogDeliveryRecordMapper.updateById(recordDO);
        }
    }
}

