package com.nspace.group.module.logs.service.delivery.dto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 日志投递任务DTO集合
 *
 * <AUTHOR>
 */
public class LogDeliveryTasksRunDTO {

    // 日志数据
    private final List<LogDeliveryTaskDTO> deliveryTasks;

    private final int totalLogCount;

    private final LocalDateTime logMaxTime;

    public LogDeliveryTasksRunDTO(List<LogDeliveryTaskDTO> deliveryTasks, int totalLogCount, LocalDateTime logMaxTime) {
        this.deliveryTasks = deliveryTasks;
        this.totalLogCount = totalLogCount;
        this.logMaxTime = logMaxTime;
    }

    public List<LogDeliveryTaskDTO> getDeliveryTasks() {
        return deliveryTasks;
    }

    public int getTotalLogCount() {
        return totalLogCount;
    }

    public LocalDateTime getLogMaxTime() {
        return logMaxTime;
    }
}
