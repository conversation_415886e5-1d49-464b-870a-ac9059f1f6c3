package com.nspace.group.module.logs.config.delivery;

import com.aliyun.openservices.aliyun.log.producer.LogProducer;
import com.aliyun.openservices.aliyun.log.producer.Producer;
import com.aliyun.openservices.aliyun.log.producer.ProducerConfig;
import com.aliyun.openservices.aliyun.log.producer.ProjectConfig;
import com.nspace.group.module.infra.enums.user.UserCloudServiceTypeEnum;
import com.nspace.group.module.infra.service.user.UserCloudServerService;
import com.nspace.group.module.infra.service.user.dto.LogDeliveryDomainDTO;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class AliyunLogProducerConfig {

    private final UserCloudServerService userCloudServerService;

    // 通过构造器注入 UserCloudServerService
    @Autowired
    public AliyunLogProducerConfig(UserCloudServerService userCloudServerService) {
        this.userCloudServerService = userCloudServerService;
    }

    /**
     * 配置 AliyunLogProducer
     */
    @Bean
    public Producer aliCloudLogProducer() {
        List<LogDeliveryDomainDTO> deliveryParams = userCloudServerService.getLogDeliveryConfigList(
                UserCloudServiceTypeEnum.GYCDN.getCode(),
                LogDeliveryTargetEnum.CDN_ALI.getVendorCode()
        );
        Producer producer = new LogProducer(new ProducerConfig());
        deliveryParams.forEach(deliveryParam -> {
            String project = deliveryParam.getProject();
            String apiBaseUrl = deliveryParam.getEndpoint();
            String accessKey = deliveryParam.getAk();
            String privateKey = deliveryParam.getSk();
            producer.putProjectConfig(new ProjectConfig(project, apiBaseUrl, accessKey, privateKey));
        });
        return producer;
    }

}
