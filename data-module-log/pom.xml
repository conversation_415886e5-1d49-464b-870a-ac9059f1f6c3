<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.nspace.group</groupId>
        <artifactId>data-works</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>data-module-log</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>
        日志模块
    </description>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-module-infra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-spring-boot-starter-file</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log-producer</artifactId>
        </dependency>
    </dependencies>

</project>