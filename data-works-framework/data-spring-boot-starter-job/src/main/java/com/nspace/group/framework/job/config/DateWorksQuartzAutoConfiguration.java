package com.nspace.group.framework.job.config;

import com.nspace.group.framework.job.core.scheduler.SchedulerManager;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Optional;

/**
 * 定时任务 Configuration

 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/30
 * @time：16:10
 */
@AutoConfiguration
@EnableScheduling
@Slf4j
public class DateWorksQuartzAutoConfiguration {

    @Bean
    public SchedulerManager schedulerManager(Optional<Scheduler> scheduler) {
        return new SchedulerManager(scheduler.get());
    }

}
