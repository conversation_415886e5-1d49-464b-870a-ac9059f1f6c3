package com.nspace.group.framework.common.util.date;

import cn.hutool.core.date.LocalDateTimeUtil;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.Calendar;
import java.util.Date;

/**
 * DateUtils 时间工具类
 */
public class DateUtils {

    /**
     * 时区 - 默认
     */
    private static final String TIME_ZONE_DEFAULT = "GMT+8";

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    private static final DateTimeFormatter outputFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

    /**
     * 秒转换成毫秒
     */
    private static final long SECOND_MILLIS = 1000;

    private static final String FORMAT_YEAR_MONTH_DAY = "yyyy-MM-dd";

    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter FLEXIBLE_FORMATTER = new DateTimeFormatterBuilder()
            .appendPattern("yyyy-MM-dd HH:mm:ss")
            .optionalStart()
            .appendFraction(ChronoField.MILLI_OF_SECOND, 0, 3, true)
            .optionalEnd()
            .toFormatter();

    public static Date of(LocalDateTime date) {
        if (date == null) {
            return null;
        }
        ZonedDateTime zonedDateTime = date.atZone(ZoneId.systemDefault());
        Instant instant = zonedDateTime.toInstant();
        return Date.from(instant);
    }

    public static LocalDateTime now() {
        return LocalDateTime.now();
    }

    /**
     * 比较两个日期，返回较大的那个
     */
    public static Date max(Date a, Date b) {
        if (a == null) return b;
        if (b == null) return a;
        return a.compareTo(b) > 0 ? a : b;
    }

    public static LocalDateTime max(LocalDateTime a, LocalDateTime b) {
        if (a == null) return b;
        if (b == null) return a;
        return a.isAfter(b) ? a : b;
    }

    /**
     * 判断是否为今天
     */
    public static boolean isToday(LocalDateTime date) {
        return LocalDateTimeUtil.isSameDay(date, LocalDateTime.now());
    }

    /**
     * 判断是否为昨天
     */
    public static boolean isYesterday(LocalDateTime date) {
        return LocalDateTimeUtil.isSameDay(date, LocalDateTime.now().minusDays(1));
    }

    /**
     * 将字符串类型的时间转换为 LocalDateTime
     */
    public static LocalDateTime stringToLocalDateTime(String time) {
        return LocalDateTimeUtil.parse(time, FLEXIBLE_FORMATTER);
    }

    /**
     * 在指定时间上添加指定的分钟数
     */
    public static LocalDateTime addMinute(String queryTime, int minute) {
        LocalDateTime dateTime = LocalDateTime.parse(queryTime, DEFAULT_FORMATTER);
        return dateTime.plusMinutes(minute);
    }

    /**
     * 获取系统当前时间的格式化字符串
     */
    public static String getSystemTime() {
        return LocalDateTime.now().format(DEFAULT_FORMATTER);
    }

    /**
     * 获取指定时间前几分钟的时间字符串
     */
    public static String minusMinutesToStreamTime(String queryTime, int minute) {
        LocalDateTime dateTime = LocalDateTime.parse(queryTime, DEFAULT_FORMATTER);
        return dateTime.minusMinutes(minute).format(DEFAULT_FORMATTER);
    }

    public static String plusMinutesToStreamTime(String queryTime, int minute) {
        LocalDateTime dateTime = LocalDateTime.parse(queryTime, DEFAULT_FORMATTER);
        return dateTime.plusMinutes(minute).format(DEFAULT_FORMATTER);
    }

    public static String convertToCustomFormat(String dateTimeStr, String format) {
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(format);
        LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, FLEXIBLE_FORMATTER);
        return dateTime.format(outputFormatter);
    }

    public static String minusSecondToStreamTime(String queryTime, int second) {
        LocalDateTime dateTime = LocalDateTime.parse(queryTime, DEFAULT_FORMATTER);
        return dateTime.minusSeconds(second).format(DEFAULT_FORMATTER);
    }

    /**
     * 计算两个时间的差值，单位秒
     *
     * @param beginPushTime
     * @param logTime
     * @return
     */
    public static long calculateTimeDifferenceInSeconds(String beginPushTime, String logTime) {
        LocalDateTime beginTime = LocalDateTime.parse(beginPushTime, formatter);
        LocalDateTime logDateTime = LocalDateTime.parse(logTime, formatter);
        Duration duration = Duration.between(beginTime, logDateTime);
        return duration.getSeconds();
    }


    public static String convertToISO8601(String dateStr, String zoneOffsetStr) {
        try {
            LocalDateTime localDateTime = LocalDateTime.parse(dateStr, formatter);
            ZoneOffset zoneOffset = ZoneOffset.of(zoneOffsetStr);
            OffsetDateTime offsetDateTime = localDateTime.atOffset(zoneOffset);
            return offsetDateTime.format(outputFormatter);
        } catch (Exception e) {
            return "Invalid input: " + e.getMessage();
        }
    }

    public static LocalDateTime convertToLocalDateTime(long timestampMillis) {
        return Instant.ofEpochMilli(timestampMillis)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

}