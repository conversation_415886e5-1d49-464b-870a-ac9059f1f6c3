package com.nspace.group.framework.common.enums;

import com.nspace.group.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

@RequiredArgsConstructor
@Getter
public enum DataPlatformEnum implements IntArrayValuable {

    PLATFORM_NSPACE(0, "NSPACE"), //nspace 内部平台
    PLATFORM_TENCENT(1, "TX"), //腾讯云
    PLATFORM_CTYUN(2, "CT"), //中国电信
    PLATFORM_QINIU(3, "QN"), //七牛云
    PLATFORM_ALI(4, "ALI"); //阿里云

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DataPlatformEnum::getPlatform).toArray();

    /**
     * 平台
     */
    private final Integer platform;
    /**
     * 平台code
     */
    private final String code;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 转换为小写的平台code，db里面保存的是小写
     * @return
     */
    public static List<String> getQueryVendorCodes() {
        return List.of(PLATFORM_TENCENT.getCode().toLowerCase(), PLATFORM_CTYUN.getCode().toLowerCase());
    }
}
