package com.nspace.group.framework.file.core.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.nspace.group.framework.file.config.DataWorksFileConfigProperties;
import com.nspace.group.framework.file.core.FileClient;
import com.nspace.group.framework.file.core.dto.FilePresignedUrlRespDTO;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.*;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.time.ZonedDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :FileClientImpl.java, v0.1 2024年06月27日 17:50 Exp
 */
@Slf4j
public class FileClientImpl implements FileClient {

    private MinioClient client;

    private final DataWorksFileConfigProperties config;

    private final ReentrantLock lock = new ReentrantLock();


    public FileClientImpl(DataWorksFileConfigProperties config) {
        this.config = config;
        doInit();
    }

    private void doInit() {
        // 初始化客户端
        client = MinioClient.builder()
                .endpoint(buildEndpointURL()) // Endpoint URL
                .credentials(config.getAccessKey(), config.getSecretKey()) // 认证密钥
                .build();
        client.disableVirtualStyleEndpoint();
    }

    /**
     * 基于 endpoint 构建调用云服务的 URL 地址
     *
     * @return URI 地址
     */
    private String buildEndpointURL() {
        // 如果已经是 http 或者 https，则不进行拼接.主要适配 MinIO
        if (HttpUtil.isHttp(config.getEndpoint()) || HttpUtil.isHttps(config.getEndpoint())) {
            return config.getEndpoint();
        }
        return StrUtil.format("https://{}", config.getEndpoint());
    }

    @Override
    public String upload(byte[] content, String bucket, String path, String type, Map<String, String> tagMap) throws Exception {
        // 执行上传
        client.putObject(PutObjectArgs.builder()
                .bucket(bucket) // bucket 必须传递
                .contentType(type)
                .tags(tagMap)
                .object(path) // 相对路径作为 key
                .stream(new ByteArrayInputStream(content), content.length, -1) // 文件内容
                .build());
        // 拼接返回路径
        return config.getDomain() + "/" + bucket + "/" + path;
    }

    @Override
    public void delete(String bucket, String path) throws Exception {
        client.removeObject(RemoveObjectArgs.builder()
                .bucket(bucket) // bucket 必须传递
                .object(path) // 相对路径作为 key
                .build());
    }

    @Override
    public byte[] getContent(String bucket, String path) throws Exception {
        GetObjectResponse response = client.getObject(GetObjectArgs.builder()
                .bucket(bucket) // bucket 必须传递
                .object(path) // 相对路径作为 key
                .build());
        return IoUtil.readBytes(response);
    }

    @Override
    public void checkCreateBucket(String bucket, String policy) throws Exception {
        try {
            lock.lock();
            boolean isExists = client.bucketExists(BucketExistsArgs.builder().bucket(bucket).build());
            if (!isExists) {
                log.info("checkCreateBucket,begin_create_bucket,bucket={},policy={}", bucket, policy);
                client.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
                client.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucket).config(policy).build());
                log.info("checkCreateBucket,end_create_bucket,bucket={},policy={}", bucket, policy);
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void setBucketExpiration(String bucket, Integer days) throws Exception {
        List<LifecycleRule> rules = new LinkedList<>();
        LifecycleRule expirationRule = new LifecycleRule(Status.ENABLED,
                null,
                new Expiration((ZonedDateTime) null, days, null),
                new RuleFilter(""),
                String.format("expireIn%sDaysRule", days),
                null,
                null,
                null);
        rules.add(expirationRule);
        LifecycleConfiguration config = new LifecycleConfiguration(rules);
        client.setBucketLifecycle(SetBucketLifecycleArgs.builder().bucket(bucket).config(config).build());
    }

    @Override
    public FilePresignedUrlRespDTO getPresignedObjectUploadUrl(String bucket, String path) throws Exception {
        String uploadUrl = client.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                .method(Method.PUT)
                .bucket(bucket)
                .object(path)
                .expiry(10, TimeUnit.MINUTES) // 过期时间（秒数）取值范围：1 秒 ~ 7 天
                .build()
        );
        return new FilePresignedUrlRespDTO(uploadUrl, config.getDomain() + "/" + bucket + "/" + path);
    }


    /**
     * 获得文件预签名下载地址
     *
     * @param path 相对路径
     * @return 文件预签名地址
     */
    @Override
    public FilePresignedUrlRespDTO getPresignedObjectDownloadUrl(String bucket, String path) throws Exception {
        String downloadUrl = client.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                .method(Method.GET)
                .bucket(bucket)
                .object(path)
                .expiry(10, TimeUnit.MINUTES) // 过期时间（秒数）取值范围：1 秒 ~ 7 天
                .build()
        );
        return new FilePresignedUrlRespDTO("", downloadUrl);
    }

    @Override
    public void merge(String bucket, String path, Map<String, String> tagMap, List<String[]> sourceInfoList) throws Exception {
        List<ComposeSource> sourceObjectList = sourceInfoList.stream()
                .map(sourceInfo -> ComposeSource.builder().bucket(sourceInfo[0]).object(sourceInfo[1]).build())
                .collect(Collectors.toList());
        client.composeObject(
                ComposeObjectArgs.builder()
                        .bucket(bucket)
                        .object(path)
                        .tags(tagMap)
                        .sources(sourceObjectList)
                        .build());
    }
}
