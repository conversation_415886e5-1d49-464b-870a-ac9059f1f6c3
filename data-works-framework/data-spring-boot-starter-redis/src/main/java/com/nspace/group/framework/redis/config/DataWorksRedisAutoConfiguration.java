package com.nspace.group.framework.redis.config;

import cn.hutool.core.util.ReflectUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.redisson.spring.starter.RedissonAutoConfiguration;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Redis 配置类
 *
 * <AUTHOR>
 */
@Configuration
@AutoConfiguration(before = RedissonAutoConfiguration.class)
@Slf4j
public class DataWorksRedisAutoConfiguration {

    @Value("${spring.redis.sentinel.check-sentinels-list}")
    private boolean checkSentinelsList;

    @Autowired
    private RedisProperties redisProperties;

    @Bean
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory factory) {
        return new StringRedisTemplate(factory);
    }

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        List<String> nodeList = redisProperties.getSentinel().getNodes().stream().map(x ->
                x.replace("'", "").replace("]", "").replace("[", "")).collect(Collectors.toList());
        config.useSentinelServers()
                .setMasterName(redisProperties.getSentinel().getMaster())
                .addSentinelAddress(nodeList.toArray(new String[0]))
                .setPassword(redisProperties.getSentinel().getPassword())
                .setDatabase(redisProperties.getDatabase())
                .setTimeout((int) redisProperties.getTimeout().toMillis())
                .setCheckSentinelsList(checkSentinelsList);
        return Redisson.create(config);
    }


    /**
     * 创建 RedisTemplate Bean，使用 JSON 序列化方式
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        template.setKeySerializer(RedisSerializer.string());
        template.setHashKeySerializer(RedisSerializer.string());
        template.setValueSerializer(buildRedisSerializer());
        template.setHashValueSerializer(buildRedisSerializer());
        try {
            if (factory.getConnection().isClosed()) {
                log.error("Redis 连接池未初始化或已关闭");
            } else {
                String pingResponse = factory.getConnection().ping();
                log.info("Redis 链接成功:{}", pingResponse);
            }
        } catch (Exception e) {
            log.error("Redis 链接失败:", e);
        }
        return template;
    }

    public static RedisSerializer<?> buildRedisSerializer() {
        RedisSerializer<Object> json = RedisSerializer.json();
        ObjectMapper objectMapper = (ObjectMapper) ReflectUtil.getFieldValue(json, "mapper");
        objectMapper.registerModules(new JavaTimeModule());
        return json;
    }
}

