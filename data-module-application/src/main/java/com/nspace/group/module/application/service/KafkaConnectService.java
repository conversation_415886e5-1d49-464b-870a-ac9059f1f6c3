package com.nspace.group.module.application.service;

import java.util.List;

/**
 * kafka connect任务处理
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2025/1/7
 * @time：16:25
 */
public interface KafkaConnectService {
    /**
     * 获取所有连接器
     * @return
     */
    List<String> getAllConnectors();

    /**
     * 获取指定连接器的状态
     * @param connectorName
     * @return
     */
    Object getConnectorStatus(String connectorName);

    /**
     * 重启指定任务
     * @param connectorName
     * @param taskId
     */
    void restartTask(String connectorName, int taskId);

    /**
     *  监控并重启非运行状态的任务
     */
    void monitorAndRestartConnectors();
}
