package com.nspace.group.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 项目启动类
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/30
 * @time：17:40
 */
@EnableAsync
@SuppressWarnings("SpringComponentScan")
@SpringBootApplication(scanBasePackages = {"com.nspace.group.server", "com.nspace.group..module"})
@ComponentScan(basePackages = "com.nspace.group",
        excludeFilters = @ComponentScan.Filter(
                type = FilterType.REGEX,
                pattern = "com.nspace.group.server.*"))
public class DataWorksServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(DataWorksServerApplication.class, args);
    }
}