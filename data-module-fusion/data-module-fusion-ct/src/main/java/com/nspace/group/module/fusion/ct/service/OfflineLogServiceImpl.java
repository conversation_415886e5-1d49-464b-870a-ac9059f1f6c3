package com.nspace.group.module.fusion.ct.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.common.exception.ServiceException;
import com.nspace.group.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nspace.group.module.fusion.ct.biz.handler.AliCdnLogHandler;
import com.nspace.group.module.fusion.ct.client.api.LogApi;
import com.nspace.group.module.fusion.ct.client.api.LogSourceApi;
import com.nspace.group.module.fusion.ct.client.impl.auth.CtApiAuth;
import com.nspace.group.module.fusion.ct.client.impl.factory.CtApiClientFactory;
import com.nspace.group.module.fusion.ct.client.model.LogDetail;
import com.nspace.group.module.fusion.ct.convert.LogDetailConvert;
import com.nspace.group.module.fusion.ct.convert.LogSourceInfoConvert;
import com.nspace.group.module.fusion.ct.mq.message.VendorLogMessage;
import com.nspace.group.module.fusion.ct.mq.producer.VendorLogMsgProducer;
import com.nspace.group.module.fusion.ct.service.dto.LogDetailDTO;
import com.nspace.group.module.fusion.ct.service.dto.LogDetailResultDTO;
import com.nspace.group.module.fusion.ct.service.dto.LogSourceInfoDTO;
import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.ApiException;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.nspace.group.module.infra.enums.offlinelog.OfflineLogStatusEnum;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.offlinelog.vendor.VendorOfflineLogInfoService;
import com.nspace.group.module.infra.service.offlinelog.vendor.VendorOfflineLogProcessRecordService;
import com.nspace.group.module.infra.service.offlinelog.vendor.dto.VendorOfflineLogInfoDTO;
import com.nspace.group.module.infra.service.offlinelog.vendor.dto.VendorOfflineLogProcessRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version :OfflineLogServiceImpl.java, v0.1 2024年12月02日 11:52 Exp
 */
@Service("ctOfflineLogService")
@Slf4j
public class OfflineLogServiceImpl implements OfflineLogService {

    private static final String apiPath = "/api/v1/log_bsstime_files";

    private static final String apiBaseUrl = "https://open.ctcdn.cn";

    private static final String CDN_LOG_TOPIC = "ods_general_cdn_log_vendor";

    private static final ZoneOffset DEFAULT_OFFSET = ZoneOffset.ofHours(8);

    @Resource(name = "vendorCdnLogMsgProducer")
    VendorLogMsgProducer vendorLogMsgProducer;

    @Resource
    VendorOfflineLogInfoService offlineLogInfoService;

    @Resource
    VendorOfflineLogProcessRecordService processRecordService;

    @Override
    public void processOfflineLog(VendorAccountWithDomainsDTO accountWithDomain, Integer interval, Integer offset, Long startTimestamp) {

        Long tenantId = accountWithDomain.getBindTenantId();
        String domain = accountWithDomain.getDomain();
        String platform = accountWithDomain.getPlatform();
        String host = getHostFromEndpoint(apiBaseUrl);
        String accessKey = accountWithDomain.getSecretId();
        String secretKey = accountWithDomain.getSecretKey();

        //获取本次日志处理记录
        VendorOfflineLogProcessRecordDTO nextProcessRecord = processRecordService.getNextProcessRecord(tenantId, domain, platform, interval, offset, startTimestamp);
        //获取之前失败的日志处理记录，限定tenantId、domain、platform
        List<VendorOfflineLogProcessRecordDTO> failedProcessRecords = processRecordService.getFailedProcessRecords(tenantId, domain, platform);

        //本次&失败的日志文件处理记录集合
        List<VendorOfflineLogProcessRecordDTO> mergedProcessRecords = new ArrayList<>();

        //新旧日志文件处理记录合并，统一处理
        mergedProcessRecords.add(nextProcessRecord);
        mergedProcessRecords.addAll(failedProcessRecords);

        //开始时间结束时间都是以秒为单位
        //获取开始时间晚于当前时间的运行记录
        LocalDateTime now = LocalDateTime.now(DEFAULT_OFFSET).truncatedTo(ChronoUnit.SECONDS);
        Map<Boolean, List<VendorOfflineLogProcessRecordDTO>> validInvalidRecordsMap = mergedProcessRecords.stream()
                .collect(Collectors.partitioningBy(processRecord -> processRecord.getStartTime().isBefore(now)));
        validInvalidRecordsMap.get(Boolean.FALSE)
                .forEach(invalidRecord ->
                        log.warn("processOfflineLog,start_time_invalid,tenant_id={},domain={},platform={},start_time={},now_time={},skip",
                                tenantId, domain, platform, invalidRecord.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                                now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                );


        validInvalidRecordsMap.get(Boolean.TRUE).forEach(processRecord -> {
            LocalDateTime startTime = processRecord.getStartTime();
            Long[] startAndEnd = generateStartEnd(startTime, interval);
            long start = startAndEnd[0];
            long end = startAndEnd[1];
            //拼接鉴权对象
            Authentication auth = new CtApiAuth(accessKey, secretKey, apiPath, getRawQueryString(start, end, domain), host, StringPool.EMPTY);

            try {
                //调用天翼API获取日志文件信息
                log.info("begin_log_detail_fetching,tenant_id={},domain={},platform={}", tenantId, domain, platform);
                LogDetailDTO logDetail = fetchLogDetail(apiBaseUrl, start, end, domain, auth);
                log.info("end_log_detail_fetching,tenant_id={},domain={},platform={}", tenantId, domain, platform);

                List<LogDetailResultDTO> result = Objects.isNull(logDetail) ? Collections.emptyList()
                        : CollectionUtil.defaultIfEmpty(logDetail.getResult(), Collections.emptyList());
                List<LogSourceInfoDTO> logInfoList = new ArrayList<>();
                List<String> urlsForDomain = result.stream()
                        .filter(resultDTO -> !CollectionUtils.isEmpty(resultDTO.getLogs()))
                        .peek(resultDTO -> logInfoList.addAll(resultDTO.getLogs()))
                        .flatMap(resultDTO -> {
                            List<LogSourceInfoDTO> logs = resultDTO.getLogs();
                            return Objects.isNull(logs) ? Stream.empty() : logs.stream().map(LogSourceInfoDTO::getUrl);
                        }).collect(Collectors.toList());
                List<VendorOfflineLogInfoDTO> processedLogInfos = offlineLogInfoService.getLogInfosByUrls(tenantId, domain, platform, urlsForDomain);

                Set<String> dbLogInfoFileUrls = processedLogInfos.stream().map(VendorOfflineLogInfoDTO::getFileUrl).collect(Collectors.toSet());
                //过滤掉新获取到的日志文件信息中已经处理过的数据
                List<VendorOfflineLogInfoDTO> newLogInfos = logInfoList.stream()
                        .filter(logInfo -> !dbLogInfoFileUrls.contains(logInfo.getUrl()))
                        .map(logInfo -> LogSourceInfoConvert.INSTANCE.getOfflineLogInfo(tenantId, domain, platform, logInfo))
                        .collect(Collectors.toList());
                newLogInfos.forEach(vendorLogInfo -> {
                    String fileUrl = vendorLogInfo.getFileUrl();
                    log.info("begin_log_source_fetching,tenant_id={},domain={},platform={},url={}", tenantId, domain, platform, fileUrl);
                    Object logData = fetchLogSource(fileUrl);
                    List<Map<String, String>> logMapList = AliCdnLogHandler.preprocess(logData);
                    log.info("end_log_source_fetching,tenant_id={},domain={},platform={},url={}", tenantId, domain, platform, fileUrl);
                    List<VendorLogMessage> logMessages = AliCdnLogHandler.transformLogs(logMapList);
                    log.info("logTransformService.transformLogs,log_messages_generated,count={}", logMessages.size());
                    logMessages.forEach(vendorLogMessage ->
                            vendorLogMsgProducer.send(CDN_LOG_TOPIC, vendorLogMessage)
                    );
                    //处理成功
                    vendorLogInfo.setStatus(OfflineLogStatusEnum.SUCCESS.getStatus());
                    //新增日志文件新信息
                    log.info("VendorOfflineLogInfoService.saveLogInfo,tenant_id={},domain={},platform={},file_url={}",
                            tenantId, domain, platform, vendorLogInfo.getFileUrl());
                    offlineLogInfoService.saveLogInfo(vendorLogInfo);
                });
                //处理成功
                processRecord.setStatus(OfflineLogStatusEnum.SUCCESS.getStatus());
            } catch (Exception e) {
                //处理失败，保存失败记录
                log.error("processOfflineLog,log_process_error,start_time={},error_msg={},save_record_and_continue", startTime, e.getLocalizedMessage());
                processRecord.setStatus(OfflineLogStatusEnum.FAILED.getStatus());
            }
            //新增或修改处理记录
            log.info("VendorOfflineLogProcessRecordService.saveProcessRecord,tenant_id={},domain={},platform={},start_time={},status={}",
                    tenantId, domain, platform, startTime, processRecord.getStatus());
            processRecordService.saveProcessRecord(processRecord);
        });
    }


    /**
     * 获取离线日志信息
     *
     * @param apiBasePath API基础请求地址
     * @param start       查询的起始时间
     * @param end         查询的结束时间
     * @param domain      需要查询的域名
     * @param apiKeyAuth  鉴权对象
     * @return LogDetailDTO
     */
    private LogDetailDTO fetchLogDetail(String apiBasePath, Long start, Long end, String domain, Authentication apiKeyAuth) {
        //获取ApiClient
        ApiClient apiClient = CtApiClientFactory.INSTANCE.createApiClient(apiBasePath, apiKeyAuth);
        //创建LogApi对象
        LogApi logApi = new LogApi(apiClient);
        try {
            LogDetail logDetail = logApi.getLogDetail(start, end, domain);
            if (logDetail == null) return null;
            if ("100000".equals(logDetail.getCode())) {
                log.info("fetchLogDetail,log_detail_fetched,apiBasePath={},start={},end={},domain={},code={},result_list={}",
                        apiBasePath, start, end, domain, logDetail.getCode(), logDetail.getResult());
                return LogDetailConvert.INSTANCE.toLogDetailDTO(logDetail);
            }
            throw new ServiceException(GlobalErrorCodeConstants.UNKNOWN.getCode(), logDetail.getMessage());
        } catch (ApiException e) {
            log.error("LogApi.fetchLogDetail,unknown_api_exception,api_base_path={},start={},end={},domain={}",
                    apiBasePath, start, end, domain);

            throw new RuntimeException(e);
        }
    }

    /**
     * 获取离线日志信息
     *
     * @param sourceLocation Log源文件路径
     * @return Object
     */
    private Object fetchLogSource(String sourceLocation) {
        //获取ApiClient
        ApiClient apiClient = CtApiClientFactory.INSTANCE.createApiClient(sourceLocation);
        //创建LogSourceApi对象
        LogSourceApi logSourceApi = new LogSourceApi(apiClient);
        try {
            byte[] logSourceInBytes = logSourceApi.getLogSource();
            log.info("fetchLogSource,log_source_fetched,sourceLocation={}", sourceLocation);
            return logSourceInBytes;
        } catch (ApiException e) {
            log.error("LogSourceApi.fetchLogSource,unknown_api_exception,sourceLocation={}", sourceLocation);
            throw new RuntimeException(e);
        }
    }

    private String getHostFromEndpoint(String endpoint) {
        return URLUtil.toUrlForHttp(URLUtil.normalize(endpoint)).getHost();
    }

    private String getRawQueryString(Long start, Long end, String domain) {
        return StringPool.QUESTION_MARK +
                "start_time" + StringPool.EQUALS + start.toString() +
                StringPool.AMPERSAND +
                "end_time" + StringPool.EQUALS + end.toString() +
                StringPool.AMPERSAND +
                "domain" + StringPool.EQUALS + domain;

    }

    private Long[] generateStartEnd(LocalDateTime startTime, Integer interval) {
        long start = startTime.toEpochSecond(DEFAULT_OFFSET);
        long end = startTime.plusHours(interval).toEpochSecond(DEFAULT_OFFSET);
        return new Long[]{start, end};
    }
}
