package com.nspace.group.module.fusion.ct.convert;

import com.nspace.group.module.fusion.ct.client.model.LogDetail;
import com.nspace.group.module.fusion.ct.service.dto.LogDetailDTO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version :LogDetailConvert.java, v0.1 2024年12月03日 09:01 zhangxin Exp
 */
@Mapper(uses = LogSourceInfoConvert.class)
public interface LogDetailConvert {

    LogDetailConvert INSTANCE = Mappers.getMapper(LogDetailConvert.class);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    LogDetailDTO toLogDetailDTO(LogDetail logDetail);

}
