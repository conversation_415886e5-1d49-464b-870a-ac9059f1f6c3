package com.nspace.group.module.fusion.qiniu.service;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.nspace.group.module.fusion.qiniu.convert.OdsOfflineLogStreamPullConvert;
import com.nspace.group.module.fusion.qiniu.dal.dataobject.OdsOfflineLogStreamPullDO;
import com.nspace.group.module.fusion.qiniu.dal.handler.StreamLoadHandler;
import com.nspace.group.module.fusion.qiniu.service.dto.LogDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 拉流计费离线日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OfflinePullLogServiceImpl implements OfflinePullLogService {

    @Resource
    StreamLoadHandler streamLoadHandler;

    @Override
    public void saveMany(List<LogDTO> logDTOList) {
        List<OdsOfflineLogStreamPullDO> logList = OdsOfflineLogStreamPullConvert.INSTANCE.getOfflinePullLogList(logDTOList);
        String tableName = SqlHelper.table(OdsOfflineLogStreamPullDO.class).getTableName();
        try {
            String json = OdsOfflineLogStreamPullConvert.INSTANCE.getOfflinePullLogListJson(logList);
            Map<String, String> loadResultMap = streamLoadHandler.sendData(tableName, json);
            if ("Success".equals(loadResultMap.get("Status"))) {
                log.info("saveMany,stream_load_success,NumberLoadedRows={},LoadTimeMs={}", loadResultMap.get("NumberLoadedRows"), loadResultMap.get("LoadTimeMs"));
            } else {
                log.error("saveMany,stream_load_failed,Message={},ErrorURL={}", loadResultMap.get("Message"), loadResultMap.get("ErrorURL"));
                throw new Exception("stream_load_failed");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
