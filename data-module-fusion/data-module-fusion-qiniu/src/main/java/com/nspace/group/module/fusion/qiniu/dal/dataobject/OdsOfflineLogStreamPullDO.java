package com.nspace.group.module.fusion.qiniu.dal.dataobject;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

@TableName(value = "tb_ods_offline_log_stream_pull")
public class OdsOfflineLogStreamPullDO {

    //唯一值
    private String uniqueId;

    //log 记录时间
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime logTime;

    //域名
    private String domain;

    //租户ID
    private Long tenantId;

    //请求uri
    private String requestUri;

    //日志对应的json数据信息
    private String logJson;

    //数据来源
    private Integer dataPlatform;

    //数据写入时间
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime curTimestamp;

    public LocalDateTime getLogTime() {
        return logTime;
    }

    public void setLogTime(LocalDateTime logTime) {
        this.logTime = logTime;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public String getLogJson() {
        return logJson;
    }

    public void setLogJson(String logJson) {
        this.logJson = logJson;
    }

    public Integer getDataPlatform() {
        return dataPlatform;
    }

    public void setDataPlatform(Integer dataPlatform) {
        this.dataPlatform = dataPlatform;
    }

    public LocalDateTime getCurTimestamp() {
        return curTimestamp;
    }

    public void setCurTimestamp(LocalDateTime curTimestamp) {
        this.curTimestamp = curTimestamp;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }
}
