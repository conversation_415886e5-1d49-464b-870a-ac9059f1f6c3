package com.nspace.group.module.fusion.qiniu.service;

import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import com.nspace.group.module.fusion.qiniu.biz.VendorLogPreprocessService;
import com.nspace.group.module.fusion.qiniu.biz.VendorLogTransformService;
import com.nspace.group.module.fusion.qiniu.client.api.LogApi;
import com.nspace.group.module.fusion.qiniu.client.api.LogSourceApi;
import com.nspace.group.module.fusion.qiniu.client.impl.auth.ApiKeyAuth;
import com.nspace.group.module.fusion.qiniu.client.impl.factory.QiniuApiClientFactory;
import com.nspace.group.module.fusion.qiniu.client.model.LogDetail;
import com.nspace.group.module.fusion.qiniu.convert.LogDetailConvert;
import com.nspace.group.module.fusion.qiniu.convert.LogSourceInfoConvert;
import com.nspace.group.module.fusion.qiniu.service.dto.LogDTO;
import com.nspace.group.module.fusion.qiniu.service.dto.LogDetailDTO;
import com.nspace.group.module.fusion.qiniu.service.dto.LogSourceInfoDTO;
import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.ApiException;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.nspace.group.module.infra.enums.offlinelog.OfflineLogStatusEnum;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.offlinelog.vendor.VendorOfflineLogInfoService;
import com.nspace.group.module.infra.service.offlinelog.vendor.VendorOfflineLogProcessRecordService;
import com.nspace.group.module.infra.service.offlinelog.vendor.dto.VendorOfflineLogInfoDTO;
import com.nspace.group.module.infra.service.offlinelog.vendor.dto.VendorOfflineLogProcessRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :OfflineLogServiceImpl.java, v0.1 2024年12月02日 11:52 Exp
 */
@Service("qiniuOfflineLogService")
@Slf4j
public class OfflineLogServiceImpl implements OfflineLogService {

    private static final String scheme = "Qiniu";
    private static final String apiPath = "/statd/v1/livelog";

    private static final String apiBaseUrl = "http://miku-statd.qiniuapi.com";

    private static final ZoneOffset DEFAULT_OFFSET = ZoneOffset.ofHours(8);

    @Resource
    VendorLogPreprocessService logPreprocessService;
    @Resource
    VendorLogTransformService logTransformService;

    @Resource
    OfflinePushLogService pushLogService;

    @Resource
    OfflinePullLogService pullLogService;

    @Resource
    VendorOfflineLogInfoService offlineLogInfoService;

    @Resource
    VendorOfflineLogProcessRecordService processRecordService;

    @Override
    public void processOfflineLog(VendorAccountWithDomainsDTO accountWithDomain, Integer interval, Integer offset, Long startTimestamp) {

        Long tenantId = accountWithDomain.getBindTenantId();
        String domain = accountWithDomain.getDomain();
        String platform = accountWithDomain.getPlatform();
        String host = getHostFromEndpoint(apiBaseUrl);
        String accessKey = accountWithDomain.getSecretId();
        String secretKey = accountWithDomain.getSecretKey();

        //获取本次日志处理记录
        VendorOfflineLogProcessRecordDTO nextProcessRecord = processRecordService.getNextProcessRecord(tenantId, domain, platform, interval, offset, startTimestamp);
        //获取之前失败的日志处理记录，限定tenantId、domain、platform
        List<VendorOfflineLogProcessRecordDTO> failedProcessRecords = processRecordService.getFailedProcessRecords(tenantId, domain, platform);

        //本次&失败的日志文件处理记录集合
        List<VendorOfflineLogProcessRecordDTO> mergedProcessRecords = new ArrayList<>();

        //新旧日志文件处理记录合并，统一处理
        mergedProcessRecords.add(nextProcessRecord);
        mergedProcessRecords.addAll(failedProcessRecords);

        //开始时间结束时间都是以秒为单位
        //获取开始时间晚于当前时间的运行记录
        LocalDateTime now = LocalDateTime.now(DEFAULT_OFFSET).truncatedTo(ChronoUnit.SECONDS);
        Map<Boolean, List<VendorOfflineLogProcessRecordDTO>> validInvalidRecordsMap = mergedProcessRecords.stream()
                .collect(Collectors.partitioningBy(processRecord -> processRecord.getStartTime().isBefore(now)));
        validInvalidRecordsMap.get(Boolean.FALSE)
                .forEach(invalidRecord ->
                        log.warn("processOfflineLog,start_time_invalid,tenant_id={},domain={},platform={},start_time={},now_time={},skip",
                                tenantId, domain, platform, invalidRecord.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                                now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                );


        validInvalidRecordsMap.get(Boolean.TRUE).forEach(processRecord -> {
            LocalDateTime startTime = processRecord.getStartTime();
            Long[] startAndEnd = generateStartEnd(startTime, interval);
            long start = startAndEnd[0];
            long end = startAndEnd[1];
            String rawQuery = getRawQueryString(start, end, domain);
            //拼接鉴权对象
            Authentication auth = new ApiKeyAuth(scheme, accessKey, secretKey,
                    HttpMethod.GET.name(), apiPath, apiBaseUrl + apiPath + rawQuery, rawQuery, host,
                    StringPool.EMPTY, StringPool.EMPTY, StringPool.EMPTY);

            try {
                //调用七牛云API获取日志文件信息
                log.info("begin_log_detail_fetching,tenant_id={},domain={},platform={}", tenantId, domain, platform);
                LogDetailDTO logDetail = fetchLogDetail(apiBaseUrl, start, end, domain, auth);
                log.info("end_log_detail_fetching,tenant_id={},domain={},platform={}", tenantId, domain, platform);

                List<LogSourceInfoDTO> logInfoList = Objects.isNull(logDetail.getLogInfoList()) ?
                        Collections.emptyList() : logDetail.getLogInfoList();
                List<String> urlsForDomain = logInfoList.stream().map(LogSourceInfoDTO::getUrl).collect(Collectors.toList());
                List<VendorOfflineLogInfoDTO> processedLogInfos = offlineLogInfoService.getLogInfosByUrls(tenantId, domain, platform, urlsForDomain);

                Set<String> dbLogInfoFileUrls = processedLogInfos.stream().map(VendorOfflineLogInfoDTO::getFileUrl).collect(Collectors.toSet());
                //过滤掉新获取到的日志文件信息中已经处理过的数据
                List<VendorOfflineLogInfoDTO> newLogInfos = logInfoList.stream()
                        .filter(logInfo -> !dbLogInfoFileUrls.contains(logInfo.getUrl()))
                        .map(logInfo -> LogSourceInfoConvert.INSTANCE.getOfflineLogInfo(tenantId, domain, platform, logInfo))
                        .collect(Collectors.toList());
                newLogInfos.forEach(vendorLogInfo -> {
                    String fileUrl = vendorLogInfo.getFileUrl();
                    log.info("begin_log_source_fetching,tenant_id={},domain={},platform={},url={}", tenantId, domain, platform, fileUrl);
                    Object logData = fetchLogSource(fileUrl);
                    List<List<String>> logSplitsList = logPreprocessService.preprocess(logData);
                    log.info("end_log_source_fetching,tenant_id={},domain={},platform={},url={}", tenantId, domain, platform, fileUrl);
                    Map<String, List<LogDTO>> logTypeSplitsMap = logTransformService.transformMixedLogs(logSplitsList);
                    logTypeSplitsMap.forEach((type, logDTOList) -> logDTOList.forEach(logDTO -> logDTO.setTenantId(tenantId)));
                    List<LogDTO> pushLogs = logTypeSplitsMap.get(LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.getCode());
                    List<LogDTO> pullLogs = logTypeSplitsMap.get(LiveDomainTypeEnum.DOMAIN_TYPE_PULL.getCode());
                    log.info("transformMixedLog,logs_generated,tenant_id={},domain={},platform={},url={},{}={},{}={}", tenantId, domain, platform, fileUrl,
                            LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.getCode(), pushLogs.size(),
                            LiveDomainTypeEnum.DOMAIN_TYPE_PULL.getCode(), pullLogs.size());
                    if (!pushLogs.isEmpty()) {
                        log.info("OfflinePushLogService.saveMany,save_push_log,tenant_id={},domain={},platform={},size={}",
                                tenantId, domain, platform, pushLogs.size());
                        //stream load方式导入数据
                        pushLogService.saveMany(pushLogs);
                    }
                    if (!pullLogs.isEmpty()) {
                        log.info("OfflinePullLogService.saveMany,save_pull_log,tenant_id={},domain={},platform={},size={}",
                                tenantId, domain, platform, pullLogs.size());
                        //stream load方式导入数据
                        pullLogService.saveMany(pullLogs);
                    }
                    //处理成功
                    vendorLogInfo.setStatus(OfflineLogStatusEnum.SUCCESS.getStatus());
                    //新增日志文件新信息
                    log.info("VendorOfflineLogInfoService.saveLogInfo,tenant_id={},domain={},platform={},file_url={}",
                            tenantId, domain, platform, vendorLogInfo.getFileUrl());
                    offlineLogInfoService.saveLogInfo(vendorLogInfo);
                });
                //处理成功
                processRecord.setStatus(OfflineLogStatusEnum.SUCCESS.getStatus());
            } catch (Exception e) {
                //处理失败，保存失败记录
                log.error("processOfflineLog,log_process_error,start_time={},error_msg={},save_record_and_continue", startTime, e.getLocalizedMessage());
                processRecord.setStatus(OfflineLogStatusEnum.FAILED.getStatus());
            }
            //新增或修改处理记录
            log.info("VendorOfflineLogProcessRecordService.saveProcessRecord,tenant_id={},domain={},platform={},start_time={},status={}",
                    tenantId, domain, platform, startTime, processRecord.getStatus());
            processRecordService.saveProcessRecord(processRecord);
        });
    }


    /**
     * 获取离线日志信息
     *
     * @param apiBasePath API基础请求地址
     * @param start       查询的起始时间
     * @param end         查询的结束时间
     * @param domain      需要查询的域名
     * @param apiKeyAuth  鉴权对象
     * @return LogDetailDTO
     */
    private LogDetailDTO fetchLogDetail(String apiBasePath, Long start, Long end, String domain, Authentication apiKeyAuth) {
        //获取ApiClient
        ApiClient apiClient = QiniuApiClientFactory.INSTANCE.createApiClient(apiBasePath, apiKeyAuth);
        //创建LogApi对象
        LogApi logApi = new LogApi(apiClient);
        try {
            LogDetail logDetail = logApi.getLogDetail(start, end, domain);
            log.info("fetchLogDetail,log_detail_fetched,apiBasePath={},start={},end={},domain={},requestID={},log_info_list={}",
                    apiBasePath, start, end, domain, logDetail.getRequestID(), logDetail.getLogInfoList());
            return LogDetailConvert.INSTANCE.toLogDetailDTO(logDetail);
        } catch (ApiException e) {
            log.error("LogApi.fetchLogDetail,unknown_api_exception,api_base_path={},start={},end={},domain={}",
                    apiBasePath, start, end, domain);

            throw new RuntimeException(e);
        }
    }

    /**
     * 获取离线日志信息
     *
     * @param sourceLocation Log源文件路径
     * @return Object
     */
    private Object fetchLogSource(String sourceLocation) {
        //获取ApiClient
        ApiClient apiClient = QiniuApiClientFactory.INSTANCE.createApiClient(sourceLocation);
        //创建LogSourceApi对象
        LogSourceApi logSourceApi = new LogSourceApi(apiClient);
        try {
            byte[] logSourceInBytes = logSourceApi.getLogSource();
            log.info("fetchLogSource,log_source_fetched,sourceLocation={}", sourceLocation);
            return logSourceInBytes;
        } catch (ApiException e) {
            log.error("LogSourceApi.fetchLogSource,unknown_api_exception,sourceLocation={}", sourceLocation);
            throw new RuntimeException(e);
        }
    }

    private String getHostFromEndpoint(String endpoint) {
        return URLUtil.toUrlForHttp(URLUtil.normalize(endpoint)).getHost();
    }

    private String getRawQueryString(Long start, Long end, String domain) {
        return StringPool.QUESTION_MARK +
                "start" + StringPool.EQUALS + start.toString() +
                StringPool.AMPERSAND +
                "end" + StringPool.EQUALS + end.toString() +
                StringPool.AMPERSAND +
                "domain" + StringPool.EQUALS + domain;

    }

    private Long[] generateStartEnd(LocalDateTime startTime, Integer interval) {
        long start = startTime.toEpochSecond(DEFAULT_OFFSET);
        long end = startTime.plusHours(interval).toEpochSecond(DEFAULT_OFFSET);
        return new Long[]{start, end};
    }
}
