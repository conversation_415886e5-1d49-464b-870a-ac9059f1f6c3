package com.nspace.group.module.fusion.qiniu.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import com.nspace.group.module.fusion.qiniu.biz.VendorLogTransformService;
import com.nspace.group.module.fusion.qiniu.biz.handler.QiniuMixedLogHandler;
import com.nspace.group.module.fusion.qiniu.biz.handler.<PERSON><PERSON>PullLogHandler;
import com.nspace.group.module.fusion.qiniu.biz.handler.<PERSON>iuPushLogHandler;
import com.nspace.group.module.fusion.qiniu.convert.LogConvert;
import com.nspace.group.module.fusion.qiniu.service.dto.LogDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version :QiniuLogTransformServiceImpl.java, v0.1 2024年11月25日 11:36 Exp
 */
@Service
@Slf4j
public class QiniuLogTransformServiceImpl implements VendorLogTransformService {

    @Override
    public List<LogDTO> transformPushLogs(List<List<String>> logSplitsList) {
        log.info("transformPushLogs,log_line_sample_before_transform,log_line={}",
                !CollectionUtil.isEmpty(logSplitsList) ? logSplitsList.get(0) : logSplitsList);
        List<Map<String, String>> logDataMapList = QiniuPushLogHandler.handle(logSplitsList);
        return LogConvert.INSTANCE.getLogDTOList(logDataMapList);

    }

    @Override
    public List<LogDTO> transformPullLogs(List<List<String>> logSplitsList) {
        log.info("transformPullLogs,log_line_sample_before_transform,log_line={}",
                !CollectionUtil.isEmpty(logSplitsList) ? logSplitsList.get(0) : logSplitsList);
        List<Map<String, String>> logDataMapList = QiniuPullLogHandler.handle(logSplitsList);
        return LogConvert.INSTANCE.getLogDTOList(logDataMapList);
    }

    @Override
    public Map<String, List<LogDTO>> transformMixedLogs(List<List<String>> logSplitsList) {
        Map<String, List<List<String>>> logTypeSplitsMap = QiniuMixedLogHandler.handle(logSplitsList);
        List<LogDTO> pushLogs = transformPushLogs(logTypeSplitsMap.getOrDefault(LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.getCode(), Collections.emptyList()));
        List<LogDTO> pullLogs = transformPullLogs(logTypeSplitsMap.getOrDefault(LiveDomainTypeEnum.DOMAIN_TYPE_PULL.getCode(), Collections.emptyList()));
        HashMap<String, List<LogDTO>> map = new HashMap<>(2);
        map.put(LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.getCode(), pushLogs);
        map.put(LiveDomainTypeEnum.DOMAIN_TYPE_PULL.getCode(), pullLogs);
        return map;
    }
}