package com.nspace.group.module.fusion.qiniu.convert;

import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.module.fusion.qiniu.client.model.LogSourceInfo;
import com.nspace.group.module.fusion.qiniu.service.dto.LogSourceInfoDTO;
import com.nspace.group.module.infra.service.offlinelog.vendor.dto.VendorOfflineLogInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

/**
 * <AUTHOR>
 * @version :LogDetailConvert.java, v0.1 2024年12月03日 09:01 zhangxin Exp
 */
@Mapper
public interface LogSourceInfoConvert {

    LogSourceInfoConvert INSTANCE = Mappers.getMapper(LogSourceInfoConvert.class);

    LogSourceInfoDTO toLogInfoDTO(LogSourceInfo logSourceInfo);

    List<LogSourceInfoDTO> toLogInfoDTOList(List<LogSourceInfo> logSourceInfoList);

    @Mapping(target = "fileTime", expression = "java(getFileTime(logInfo.getUrl()))")
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "generateTime", expression = "java(logInfo.getTime().toLocalDateTime())")
    @Mapping(target = "fileUrl", source = "logInfo.url")
    @Mapping(target = "fileSize", source = "logInfo.fsize")
    VendorOfflineLogInfoDTO getOfflineLogInfo(Long tenantId, String domain, String platform, LogSourceInfoDTO logInfo);

    default LocalDateTime getFileTime(String fileUrl) {
        String normalizedUrl = URLUtil.normalize(fileUrl);
        String path = URLUtil.getPath(normalizedUrl);
        String[] segments = path.split(StringPool.UNDERSCORE);
        String lastSegment = segments[segments.length - 1];
        String secondsInUrl = lastSegment.substring(0, lastSegment.lastIndexOf(".log.gz"));
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(secondsInUrl)), ZoneOffset.ofHours(8));
    }
}
